// /*
//  * INTEGRATION EXAMPLE
//  * 
//  * This file shows how to integrate the scripting system with your main mod.
//  * Copy the relevant parts to your main mod class.
//  */
//
// using MelonLoader;
// using TestLE.Scripting;
// using TestLE.Routine;
//
// namespace TestLE;
//
// // Example of how to modify your main mod class to support scripting
// public class ExampleModWithScripting : MelonMod
// {
//     private MainRoutine? _mainRoutine;
//     private object? _mainRoutineCoroutine;
//
//     public override void OnInitializeMetaData()
//     {
//         // Initialize the scripting system early
//         ScriptingIntegration.Initialize();
//     }
//
//     public override void OnApplicationStart()
//     {
//         MelonLogger.Msg("TestLE mod starting...");
//         
//         // The scripting system is already initialized from OnInitializeMetaData
//         // You can check status here
//         var status = ScriptingIntegration.GetStatus();
//         MelonLogger.Msg($"Scripting Status: {status}");
//     }
//
//     public override void OnSceneWasLoaded(int buildIndex, string sceneName)
//     {
//         // Update current scene for scripts
//         Globals.CURRENT_SCENE = sceneName;
//         
//         // Initialize main routine with scriptable task factory
//         if (_mainRoutine == null)
//         {
//             // Use the scriptable task factory instead of the regular one
//             var taskFactory = ScriptingIntegration.GetTaskFactory();
//             _mainRoutine = new MainRoutine(taskFactory);
//             Globals.MAIN_ROUTINE = _mainRoutine;
//         }
//     }
//
//     public override void OnUpdate()
//     {
//         // Your regular update logic here
//         
//         // Example: Reload scripts with a hotkey
//         if (Input.GetKeyDown(KeyCode.F9))
//         {
//             MelonLogger.Msg("Reloading scripts...");
//             ScriptingIntegration.ReloadScripts();
//         }
//         
//         // Example: Toggle scriptable tasks with a hotkey
//         if (Input.GetKeyDown(KeyCode.F10))
//         {
//             var status = ScriptingIntegration.GetStatus();
//             ScriptingIntegration.SetScriptableTasksEnabled(!status.ScriptableTasksEnabled);
//         }
//     }
//
//     public override void OnApplicationQuit()
//     {
//         // Shutdown the scripting system
//         ScriptingIntegration.Shutdown();
//         MelonLogger.Msg("TestLE mod shutting down...");
//     }
//
//     // Example console commands for script management
//     [ConsoleCommand("script_reload", "Reload all scripts")]
//     public static void ReloadScripts()
//     {
//         ScriptingIntegration.ReloadScripts();
//     }
//
//     [ConsoleCommand("script_enable", "Enable a script")]
//     public static void EnableScript(string scriptName)
//     {
//         if (ScriptingIntegration.EnableScript(scriptName))
//         {
//             MelonLogger.Msg($"Enabled script: {scriptName}");
//         }
//         else
//         {
//             MelonLogger.Error($"Failed to enable script: {scriptName}");
//         }
//     }
//
//     [ConsoleCommand("script_disable", "Disable a script")]
//     public static void DisableScript(string scriptName)
//     {
//         if (ScriptingIntegration.DisableScript(scriptName))
//         {
//             MelonLogger.Msg($"Disabled script: {scriptName}");
//         }
//         else
//         {
//             MelonLogger.Error($"Failed to disable script: {scriptName}");
//         }
//     }
//
//     [ConsoleCommand("script_priority", "Set script priority")]
//     public static void SetScriptPriority(string scriptName, int priority)
//     {
//         if (ScriptingIntegration.SetScriptPriority(scriptName, priority))
//         {
//             MelonLogger.Msg($"Set script '{scriptName}' priority to {priority}");
//         }
//         else
//         {
//             MelonLogger.Error($"Failed to set priority for script: {scriptName}");
//         }
//     }
//
//     [ConsoleCommand("script_status", "Show scripting system status")]
//     public static void ShowScriptStatus()
//     {
//         var status = ScriptingIntegration.GetStatus();
//         MelonLogger.Msg(status.ToString());
//         
//         if (status.IsInitialized)
//         {
//             var scripts = ScriptManager.Instance.GetLoadedScripts();
//             MelonLogger.Msg("Loaded scripts:");
//             foreach (var script in scripts)
//             {
//                 var statusText = script.IsEnabled ? "ENABLED" : "DISABLED";
//                 MelonLogger.Msg($"  - {script.Name} (Priority: {script.Priority}, Status: {statusText})");
//             }
//         }
//     }
//
//     [ConsoleCommand("script_toggle", "Toggle scriptable tasks on/off")]
//     public static void ToggleScriptableTasks()
//     {
//         var status = ScriptingIntegration.GetStatus();
//         ScriptingIntegration.SetScriptableTasksEnabled(!status.ScriptableTasksEnabled);
//         MelonLogger.Msg($"Scriptable tasks {(status.ScriptableTasksEnabled ? "disabled" : "enabled")}");
//     }
// }
//
// /*
//  * MINIMAL INTEGRATION
//  * 
//  * If you just want basic scripting support, you only need these changes:
//  */
//
// /*
// public class MinimalIntegration : MelonMod
// {
//     public override void OnInitializeMetaData()
//     {
//         // Initialize scripting
//         ScriptingIntegration.Initialize();
//     }
//
//     public override void OnSceneWasLoaded(int buildIndex, string sceneName)
//     {
//         // Use scriptable task factory
//         var taskFactory = ScriptingIntegration.GetTaskFactory();
//         Globals.MAIN_ROUTINE = new MainRoutine(taskFactory);
//     }
//
//     public override void OnApplicationQuit()
//     {
//         // Shutdown scripting
//         ScriptingIntegration.Shutdown();
//     }
// }
// */
//
// /*
//  * ADVANCED INTEGRATION TIPS
//  * 
//  * 1. Script Hot Reload:
//  *    - Scripts automatically reload when files change
//  *    - You can manually reload with ScriptingIntegration.ReloadScripts()
//  * 
//  * 2. Priority System:
//  *    - Higher priority scripts execute before lower priority ones
//  *    - Built-in tasks have priorities too (death handling = highest)
//  *    - Use priorities 200+ for critical scripts, 100-199 for normal scripts
//  * 
//  * 3. Error Handling:
//  *    - Script errors don't crash the mod
//  *    - Check console for script error messages
//  *    - Scripts with errors are automatically disabled
//  * 
//  * 4. Performance:
//  *    - CanExecute() is called every frame for enabled scripts
//  *    - Keep CanExecute() fast and simple
//  *    - Use timers/intervals to limit expensive operations
//  * 
//  * 5. Configuration:
//  *    - Scripts can be configured via config.json
//  *    - Settings persist between mod restarts
//  *    - Scripts can access their configuration parameters
//  */
