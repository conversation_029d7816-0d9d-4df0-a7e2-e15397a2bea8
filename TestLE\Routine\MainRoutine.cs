using System.Collections;
using MelonLoader;
using TestLE.Routine.Factories;
using TestLE.Routine.Interfaces;
// ReSharper disable MemberCanBePrivate.Global

namespace TestLE.Routine;

/// <summary>
/// The main orchestrator that manages and executes a prioritized list of tasks.
/// This class is OPEN for extension (by adding new IGameTask through the factory) but
/// CLOSED for modification.
/// Follows Single Responsibility Principle by focusing only on task coordination.
/// Follows Dependency Inversion Principle by depending on abstractions (IGameTask, ITaskFactory).
/// </summary>
public class MainRoutine : IEnumerator
{
    public object? Current { get; private set; }

    private readonly IEnumerable<IGameTask> _tasks;

    public MainRoutine() : this(ServiceProvider.TaskFactory)
    {
    }

    public MainRoutine(ITaskFactory taskFactory)
    {
        _tasks = taskFactory.CreateTasks();
    }

    public bool MoveNext()
    {
        if (this != MAIN_ROUTINE)
        {
            MelonLogger.Msg("MainRoutine is not the current routine!");
            return false;
        }

        if (PLAYER == null)
        {
            MelonLogger.Msg("Player is null!");
            return false;
        }

        if (CURRENT_ROUTINE == null)
        {
            MelonLogger.Msg("Current routine is null!");
            return false;
        }

        // Find the first task that can be executed and run it.
        foreach (var task in _tasks.Where(task => task.CanExecute()))
        {
            Current = task.Execute();
            return true;
        }

        MelonLogger.Msg("No tasks found!");
        return false; // Should be unreachable if DefaultWanderTask exists
    }

    public void Reset()
    {
        Current = null;
    }
}
