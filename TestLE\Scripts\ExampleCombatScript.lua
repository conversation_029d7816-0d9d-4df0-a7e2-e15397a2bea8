-- Example Combat Script
-- This demonstrates combat-related functionality

SCRIPT_NAME = "Example Combat Script"
SCRIPT_DESCRIPTION = "Demonstrates combat detection and enemy targeting"
SCRIPT_VERSION = "1.0.0"
SCRIPT_AUTHOR = "TestLE"
SCRIPT_PRIORITY = 200

-- Configuration
local COMBAT_RANGE = 15.0
local MIN_HEALTH_PERCENT = 0.3

-- State tracking
local lastCombatCheck = 0
local combatStartTime = 0
local inCombat = false

function CanExecute()
    local player = GetPlayer()
    if not player or not Player.IsAlive() then
        return false
    end
    
    -- Check if we're in combat or have enemies nearby
    local playerInCombat = Player.IsInCombat()
    local enemies = GetEnemies()
    local hasNearbyEnemies = enemies and #enemies > 0
    
    return playerInCombat or hasNearbyEnemies
end

function Execute()
    local currentTime = Unity.Time
    local player = GetPlayer()
    
    if not player then
        return false
    end
    
    local playerPos = Player.GetPosition()
    if not playerPos then
        return false
    end
    
    -- Update combat state
    local wasInCombat = inCombat
    inCombat = Player.IsInCombat()
    
    if inCombat and not wasInCombat then
        combatStartTime = currentTime
        Log("Combat started!")
    elseif not inCombat and wasInCombat then
        local combatDuration = currentTime - combatStartTime
        Log("Combat ended. Duration: " .. string.format("%.1f", combatDuration) .. " seconds")
    end
    
    -- Find and analyze enemies
    local enemies = GetEnemies()
    if enemies and #enemies > 0 then
        Log("Enemies detected: " .. #enemies)
        
        -- Find nearest enemy
        local nearestEnemy = Find.FindNearestEnemy(playerPos, COMBAT_RANGE)
        if nearestEnemy then
            -- Calculate distance to nearest enemy
            local enemyPos = nearestEnemy.transform.position
            local distance = Math.Distance(playerPos, enemyPos)
            
            Log("Nearest enemy at distance: " .. string.format("%.1f", distance))
            
            if distance <= COMBAT_RANGE then
                Log("Enemy in combat range!")
                
                -- Check if we should retreat based on health
                local healthPercent = Player.GetHealthPercent()
                if healthPercent and healthPercent < MIN_HEALTH_PERCENT then
                    Log("WARNING: Low health (" .. string.format("%.1f", healthPercent * 100) .. "%), consider retreating!")
                end
            end
        end
        
        -- Count enemies by distance
        local closeEnemies = 0
        local mediumEnemies = 0
        local farEnemies = 0
        
        for i = 1, #enemies do
            local enemy = enemies[i]
            if enemy and enemy.transform then
                local enemyPos = enemy.transform.position
                local distance = Math.Distance(playerPos, enemyPos)
                
                if distance <= 5.0 then
                    closeEnemies = closeEnemies + 1
                elseif distance <= 10.0 then
                    mediumEnemies = mediumEnemies + 1
                else
                    farEnemies = farEnemies + 1
                end
            end
        end
        
        if closeEnemies > 0 or mediumEnemies > 0 or farEnemies > 0 then
            Log("Enemy distribution - Close: " .. closeEnemies .. ", Medium: " .. mediumEnemies .. ", Far: " .. farEnemies)
        end
        
        -- Warn about overwhelming odds
        if closeEnemies >= 3 then
            Log("WARNING: Multiple close enemies detected!")
        end
    end
    
    -- Check for good shrines during combat
    if inCombat then
        local shrines = GetGoodShrines()
        if shrines and #shrines > 0 then
            local nearestShrine = Find.FindNearestInteractable(playerPos, 20.0)
            if nearestShrine then
                Log("Beneficial shrine available nearby during combat")
            end
        end
    end
    
    lastCombatCheck = currentTime
    return true
end

function OnLoad()
    Log("Combat script loaded - monitoring for combat situations")
end

function OnUnload()
    Log("Combat script unloaded")
end
