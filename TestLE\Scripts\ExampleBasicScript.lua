-- Example Basic Script
-- This demonstrates the basic structure of a TestLE script

-- Script metadata (optional but recommended)
SCRIPT_NAME = "Example Basic Script"
SCRIPT_DESCRIPTION = "A simple example script that demonstrates basic functionality"
SCRIPT_VERSION = "1.0.0"
SCRIPT_AUTHOR = "TestLE"
SCRIPT_PRIORITY = 150

-- Global variables for this script
local lastExecuteTime = 0
local executeCount = 0

-- Function to check if this script should execute
-- This is called by the routine system to determine task priority
function CanExecute()
    -- Only execute if player exists and is alive
    local player = GetPlayer()
    if not player then
        return false
    end
    
    -- Only execute every 5 seconds
    local currentTime = Unity.Time
    if currentTime - lastExecuteTime < 5.0 then
        return false
    end
    
    return true
end

-- Main execution function
-- This is called when the script is selected for execution
function Execute()
    executeCount = executeCount + 1
    lastExecuteTime = Unity.Time
    
    Log("Example script executing (count: " .. executeCount .. ")")
    
    -- Get player information
    local player = GetPlayer()
    if player then
        local position = Player.GetPosition()
        if position then
            Log("Player position: " .. position.x .. ", " .. position.y .. ", " .. position.z)
        end
        
        local health = Player.GetHealth()
        local maxHealth = Player.GetMaxHealth()
        if health and maxHealth then
            local healthPercent = (health / maxHealth) * 100
            Log("Player health: " .. health .. "/" .. maxHealth .. " (" .. string.format("%.1f", healthPercent) .. "%)")
        end
        
        Log("Player alive: " .. tostring(Player.IsAlive()))
        Log("Player in combat: " .. tostring(Player.IsInCombat()))
    end
    
    -- Check for nearby enemies
    local enemies = GetEnemies()
    if enemies then
        Log("Enemies in range: " .. #enemies)
    end
    
    -- Check for ground items
    local groundItems = GetGroundItems()
    if groundItems then
        Log("Ground items available: " .. #groundItems)
    end
    
    -- Example of using math functions
    local randomValue = Math.Random()
    Log("Random value: " .. string.format("%.3f", randomValue))
    
    -- Example of input checking
    if Unity.GetKeyDown("F1") then
        Log("F1 key was pressed!")
    end
    
    -- Return success (scripts can return values for debugging)
    return true
end

-- Optional cleanup function (called when script is unloaded)
function OnUnload()
    Log("Example script unloading...")
end

-- Optional initialization function (called when script is loaded)
function OnLoad()
    Log("Example script loaded and ready!")
end
