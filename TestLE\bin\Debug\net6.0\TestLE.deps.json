{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"TestLE/1.0.0": {"dependencies": {"MoonSharp": "2.0.0", "0Harmony": "********", "AsmResolver": "*******", "AsmResolver.DotNet": "*******", "AsmResolver.PE": "*******", "AsmResolver.PE.File": "*******", "AssetRipper.Primitives": "*******", "AssetsTools.NET": "*******", "bHapticsLib": "*******", "Iced": "********", "Il2Cpp": "*******", "Il2CppALINE": "0.0.0.0", "Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime": "0.0.0.0", "Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime": "0.0.0.0", "Il2CppBezierSolution.Runtime": "0.0.0.0", "Il2CppCinemachine": "0.0.0.0", "Il2CppCommandLine": "*******", "Il2CppCrest": "0.0.0.0", "Il2Cppcrosstales": "0.0.0.0", "Il2CppCSharp.OperationResult": "*******", "Il2CppDialogueSystem": "0.0.0.0", "Il2CppDOTween": "*******", "Il2CppDOTweenPro": "*******", "Il2CppDunGen": "0.0.0.0", "Il2CppEHRGB": "0.0.0.0", "Il2CppFacepunch.Steamworks.Win64": "*******", "Il2CppFmodShared": "0.0.0.0", "Il2CppFMODUnity": "0.0.0.0", "Il2CppFMODUnityResonance": "0.0.0.0", "Il2CppGenerated": "*******", "Il2CppHeathen.ScreenKeyboard": "0.0.0.0", "Il2CppHoudiniEngineUnity": "0.0.0.0", "Il2CppInterop.Common": "*******", "Il2CppInterop.Generator": "*******", "Il2CppInterop.HarmonySupport": "*******", "Il2CppInterop.Runtime": "*******", "Il2CppJBooth.MicroSplat.Core": "0.0.0.0", "Il2CppLE.Core": "0.0.0.0", "Il2CppLE": "0.0.0.0", "Il2CppLE.Networking.Core": "0.0.0.0", "Il2CppLE.PCG": "0.0.0.0", "Il2CppLE.Telemetry.Client": "0.0.0.0", "Il2CppLE.Telemetry": "0.0.0.0", "Il2CppLE.UI.Controls": "0.0.0.0", "Il2CppLidgren.Network": "2012.1.7.0", "Il2CppLuaInterpreter": "0.0.0.0", "Il2CppMathNet.Numerics": "4.15.0.0", "Il2CppMeshExtension": "*******", "Il2CppMono.Security": "*******", "Il2Cppmscorlib": "*******", "Il2CppNewAssembly": "0.0.0.0", "Il2CppNewtonsoft.Json": "1*******", "Il2CppNewtonsoft.Json.UnityConverters": "0.0.0.0", "Il2CppPlayFab": "0.0.0.0", "Il2CppPolly": "*******", "Il2CppProBuilderCore-Unity5": "0.0.0.0", "Il2CppProBuilderMeshOps-Unity5": "0.0.0.0", "Il2CppRewired_Core": "*******", "Il2CppRewired_Windows": "*******", "Il2CppRewired_Windows_Functions": "0.0.0.0", "Il2CppRG.ImGui": "0.0.0.0", "Il2CppRG.ImGui.Unity": "0.0.0.0", "Il2CppSirenix.OdinInspector.Attributes": "*******", "Il2CppSirenix.OdinInspector.Modules.Unity.Addressables": "0.0.0.0", "Il2CppSirenix.OdinInspector.Modules.UnityLocalization": "0.0.0.0", "Il2CppSirenix.Serialization.AOTGenerated": "0.0.0.0", "Il2CppSirenix.Serialization.Config": "*******", "Il2CppSirenix.Serialization": "*******", "Il2CppSirenix.Utilities": "*******", "Il2CppStreamChat.Core": "0.0.0.0", "Il2CppStreamChat.Libs": "0.0.0.0", "Il2CppSystem.Configuration": "*******", "Il2CppSystem.Core": "*******", "Il2CppSystem.Data": "*******", "Il2CppSystem": "*******", "Il2CppSystem.Drawing": "*******", "Il2CppSystem.IO.Compression": "*******", "Il2CppSystem.IO.Compression.FileSystem": "*******", "Il2CppSystem.Net.Http": "*******", "Il2CppSystem.Numerics": "*******", "Il2CppSystem.Runtime.CompilerServices.Unsafe": "*******", "Il2CppSystem.Runtime.Serialization": "*******", "Il2CppSystem.Xml": "*******", "Il2CppSystem.Xml.Linq": "*******", "Il2CppTriangleNET": "*******", "Il2CppUMA_Content": "0.0.0.0", "Il2CppUMA_Core": "0.0.0.0", "Il2CppUMA_Examples": "0.0.0.0", "Il2CppUniTask.Addressables": "0.0.0.0", "Il2CppUniTask": "0.0.0.0", "Il2CppXNode": "0.0.0.0", "Il2Cpp__Generated": "0.0.0.0", "IndexRange": "*******", "MelonLoader": "*******", "MelonLoader.NativeHost": "*******", "Microsoft.Bcl.AsyncInterfaces": "*******", "Microsoft.Diagnostics.NETCore.Client": "0.2.8.10101", "Microsoft.Diagnostics.Runtime": "3.1.10.12801", "Microsoft.Extensions.Configuration.Abstractions": "*******", "Microsoft.Extensions.Configuration.Binder": "*******", "Microsoft.Extensions.Configuration": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions": "*******", "Microsoft.Extensions.Logging.Abstractions": "*******", "Microsoft.Extensions.Logging": "*******", "Microsoft.Extensions.Options": "*******", "Microsoft.Extensions.Primitives": "*******", "Mono.Cecil": "********", "Mono.Cecil.Mdb": "********", "Mono.Cecil.Pdb": "********", "Mono.Cecil.Rocks": "********", "MonoMod.Backports": "*******", "MonoMod": "*********", "MonoMod.ILHelpers": "*******", "MonoMod.RuntimeDetour": "*********", "MonoMod.Utils": "*********", "Newtonsoft.Json": "1*******", "System.Configuration.ConfigurationManager": "*******", "System.Security.Cryptography.ProtectedData": "*******", "System.Security.Permissions": "*******", "System.Windows.Extensions": "*******", "Tomlet": "*******", "Unity.Addressables": "0.0.0.0", "Unity.AI.Navigation": "0.0.0.0", "Unity.Burst": "0.0.0.0", "Unity.Burst.Unsafe": "*******", "Unity.Collections": "0.0.0.0", "Unity.Collections.LowLevel.ILSupport": "0.0.0.0", "Unity.Localization": "0.0.0.0", "Unity.Mathematics": "0.0.0.0", "Unity.MemoryProfiler": "0.0.0.0", "Unity.Postprocessing.Runtime": "0.0.0.0", "Unity.Profiling.Core": "0.0.0.0", "Unity.RenderPipelines.Core.Runtime": "0.0.0.0", "Unity.ResourceManager": "0.0.0.0", "Unity.Services.Analytics": "0.0.0.0", "Unity.Services.Core.Configuration": "0.0.0.0", "Unity.Services.Core.Device": "0.0.0.0", "Unity.Services.Core": "0.0.0.0", "Unity.Services.Core.Environments": "0.0.0.0", "Unity.Services.Core.Environments.Internal": "0.0.0.0", "Unity.Services.Core.Internal": "0.0.0.0", "Unity.Services.Core.Registration": "0.0.0.0", "Unity.Services.Core.Scheduler": "0.0.0.0", "Unity.Services.Core.Telemetry": "0.0.0.0", "Unity.Services.Core.Threading": "0.0.0.0", "Unity.TextMeshPro": "0.0.0.0", "Unity.Timeline": "*******", "UnityEngine.AccessibilityModule": "0.0.0.0", "UnityEngine.AIModule": "0.0.0.0", "UnityEngine.AndroidJNIModule": "0.0.0.0", "UnityEngine.AnimationModule": "0.0.0.0", "UnityEngine.ARModule": "0.0.0.0", "UnityEngine.AssetBundleModule": "0.0.0.0", "UnityEngine.AudioModule": "0.0.0.0", "UnityEngine.ClothModule": "0.0.0.0", "UnityEngine.ContentLoadModule": "0.0.0.0", "UnityEngine.CoreModule": "0.0.0.0", "UnityEngine.CrashReportingModule": "0.0.0.0", "UnityEngine.DirectorModule": "0.0.0.0", "UnityEngine": "0.0.0.0", "UnityEngine.DSPGraphModule": "0.0.0.0", "UnityEngine.GameCenterModule": "0.0.0.0", "UnityEngine.GIModule": "0.0.0.0", "UnityEngine.GraphicsStateCollectionSerializerModule": "0.0.0.0", "UnityEngine.GridModule": "0.0.0.0", "UnityEngine.HierarchyCoreModule": "0.0.0.0", "UnityEngine.HotReloadModule": "0.0.0.0", "UnityEngine.Il2CppAssetBundleManager": "*******", "UnityEngine.Il2CppImageConversionManager": "*******", "UnityEngine.ImageConversionModule": "0.0.0.0", "UnityEngine.IMGUIModule": "0.0.0.0", "UnityEngine.InputForUIModule": "0.0.0.0", "UnityEngine.InputLegacyModule": "0.0.0.0", "UnityEngine.InputModule": "0.0.0.0", "UnityEngine.JSONSerializeModule": "0.0.0.0", "UnityEngine.LocalizationModule": "0.0.0.0", "UnityEngine.MarshallingModule": "0.0.0.0", "UnityEngine.MultiplayerModule": "0.0.0.0", "UnityEngine.ParticleSystemModule": "0.0.0.0", "UnityEngine.PerformanceReportingModule": "0.0.0.0", "UnityEngine.Physics2DModule": "0.0.0.0", "UnityEngine.PhysicsModule": "0.0.0.0", "UnityEngine.PropertiesModule": "0.0.0.0", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule": "0.0.0.0", "UnityEngine.ScreenCaptureModule": "0.0.0.0", "UnityEngine.ShaderVariantAnalyticsModule": "0.0.0.0", "UnityEngine.SharedInternalsModule": "0.0.0.0", "UnityEngine.SpriteMaskModule": "0.0.0.0", "UnityEngine.SpriteShapeModule": "0.0.0.0", "UnityEngine.StreamingModule": "0.0.0.0", "UnityEngine.SubstanceModule": "0.0.0.0", "UnityEngine.SubsystemsModule": "0.0.0.0", "UnityEngine.TerrainModule": "0.0.0.0", "UnityEngine.TerrainPhysicsModule": "0.0.0.0", "UnityEngine.TextCoreFontEngineModule": "0.0.0.0", "UnityEngine.TextCoreTextEngineModule": "0.0.0.0", "UnityEngine.TextRenderingModule": "0.0.0.0", "UnityEngine.TilemapModule": "0.0.0.0", "UnityEngine.TLSModule": "0.0.0.0", "UnityEngine.UI": "*******", "UnityEngine.UIElementsModule": "0.0.0.0", "UnityEngine.UIModule": "0.0.0.0", "UnityEngine.UmbraModule": "0.0.0.0", "UnityEngine.UnityAnalyticsCommonModule": "0.0.0.0", "UnityEngine.UnityAnalyticsModule": "0.0.0.0", "UnityEngine.UnityConnectModule": "0.0.0.0", "UnityEngine.UnityCurlModule": "0.0.0.0", "UnityEngine.UnityTestProtocolModule": "0.0.0.0", "UnityEngine.UnityWebRequestAssetBundleModule": "0.0.0.0", "UnityEngine.UnityWebRequestAudioModule": "0.0.0.0", "UnityEngine.UnityWebRequestModule": "0.0.0.0", "UnityEngine.UnityWebRequestTextureModule": "0.0.0.0", "UnityEngine.UnityWebRequestWWWModule": "0.0.0.0", "UnityEngine.VehiclesModule": "0.0.0.0", "UnityEngine.VFXModule": "0.0.0.0", "UnityEngine.VideoModule": "0.0.0.0", "UnityEngine.VRModule": "0.0.0.0", "UnityEngine.WindModule": "0.0.0.0", "UnityEngine.XRModule": "0.0.0.0", "WebSocketDotNet": "*******"}, "runtime": {"TestLE.dll": {}}}, "MoonSharp/2.0.0": {"runtime": {"lib/netstandard1.6/MoonSharp.Interpreter.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "0Harmony/********": {"runtime": {"0Harmony.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AsmResolver/*******": {"runtime": {"AsmResolver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.DotNet/*******": {"runtime": {"AsmResolver.DotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.PE/*******": {"runtime": {"AsmResolver.PE.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AsmResolver.PE.File/*******": {"runtime": {"AsmResolver.PE.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetRipper.Primitives/*******": {"runtime": {"AssetRipper.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetsTools.NET/*******": {"runtime": {"AssetsTools.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "bHapticsLib/*******": {"runtime": {"bHapticsLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Iced/********": {"runtime": {"Iced.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Il2Cpp/*******": {"runtime": {"Il2Cpp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppALINE/0.0.0.0": {"runtime": {"Il2CppALINE.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime/0.0.0.0": {"runtime": {"Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime/0.0.0.0": {"runtime": {"Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppBezierSolution.Runtime/0.0.0.0": {"runtime": {"Il2CppBezierSolution.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppCinemachine/0.0.0.0": {"runtime": {"Il2CppCinemachine.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppCommandLine/*******": {"runtime": {"Il2CppCommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppCrest/0.0.0.0": {"runtime": {"Il2CppCrest.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2Cppcrosstales/0.0.0.0": {"runtime": {"Il2Cppcrosstales.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppCSharp.OperationResult/*******": {"runtime": {"Il2CppCSharp.OperationResult.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppDialogueSystem/0.0.0.0": {"runtime": {"Il2CppDialogueSystem.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppDOTween/*******": {"runtime": {"Il2CppDOTween.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppDOTweenPro/*******": {"runtime": {"Il2CppDOTweenPro.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppDunGen/0.0.0.0": {"runtime": {"Il2CppDunGen.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppEHRGB/0.0.0.0": {"runtime": {"Il2CppEHRGB.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppFacepunch.Steamworks.Win64/*******": {"runtime": {"Il2CppFacepunch.Steamworks.Win64.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppFmodShared/0.0.0.0": {"runtime": {"Il2CppFmodShared.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppFMODUnity/0.0.0.0": {"runtime": {"Il2CppFMODUnity.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppFMODUnityResonance/0.0.0.0": {"runtime": {"Il2CppFMODUnityResonance.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppGenerated/*******": {"runtime": {"Il2CppGenerated.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppHeathen.ScreenKeyboard/0.0.0.0": {"runtime": {"Il2CppHeathen.ScreenKeyboard.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppHoudiniEngineUnity/0.0.0.0": {"runtime": {"Il2CppHoudiniEngineUnity.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppInterop.Common/*******": {"runtime": {"Il2CppInterop.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.Generator/*******": {"runtime": {"Il2CppInterop.Generator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.HarmonySupport/*******": {"runtime": {"Il2CppInterop.HarmonySupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppInterop.Runtime/*******": {"runtime": {"Il2CppInterop.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppJBooth.MicroSplat.Core/0.0.0.0": {"runtime": {"Il2CppJBooth.MicroSplat.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.Core/0.0.0.0": {"runtime": {"Il2CppLE.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE/0.0.0.0": {"runtime": {"Il2CppLE.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.Networking.Core/0.0.0.0": {"runtime": {"Il2CppLE.Networking.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.PCG/0.0.0.0": {"runtime": {"Il2CppLE.PCG.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.Telemetry.Client/0.0.0.0": {"runtime": {"Il2CppLE.Telemetry.Client.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.Telemetry/0.0.0.0": {"runtime": {"Il2CppLE.Telemetry.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLE.UI.Controls/0.0.0.0": {"runtime": {"Il2CppLE.UI.Controls.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLidgren.Network/2012.1.7.0": {"runtime": {"Il2CppLidgren.Network.dll": {"assemblyVersion": "2012.1.7.0", "fileVersion": "0.0.0.0"}}}, "Il2CppLuaInterpreter/0.0.0.0": {"runtime": {"Il2CppLuaInterpreter.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppMathNet.Numerics/4.15.0.0": {"runtime": {"Il2CppMathNet.Numerics.dll": {"assemblyVersion": "4.15.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppMeshExtension/*******": {"runtime": {"Il2CppMeshExtension.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppMono.Security/*******": {"runtime": {"Il2CppMono.Security.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2Cppmscorlib/*******": {"runtime": {"Il2Cppmscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppNewAssembly/0.0.0.0": {"runtime": {"Il2CppNewAssembly.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppNewtonsoft.Json/1*******": {"runtime": {"Il2CppNewtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "0.0.0.0"}}}, "Il2CppNewtonsoft.Json.UnityConverters/0.0.0.0": {"runtime": {"Il2CppNewtonsoft.Json.UnityConverters.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppPlayFab/0.0.0.0": {"runtime": {"Il2CppPlayFab.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppPolly/*******": {"runtime": {"Il2CppPolly.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppProBuilderCore-Unity5/0.0.0.0": {"runtime": {"Il2CppProBuilderCore-Unity5.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppProBuilderMeshOps-Unity5/0.0.0.0": {"runtime": {"Il2CppProBuilderMeshOps-Unity5.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppRewired_Core/*******": {"runtime": {"Il2CppRewired_Core.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppRewired_Windows/*******": {"runtime": {"Il2CppRewired_Windows.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppRewired_Windows_Functions/0.0.0.0": {"runtime": {"Il2CppRewired_Windows_Functions.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppRG.ImGui/0.0.0.0": {"runtime": {"Il2CppRG.ImGui.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppRG.ImGui.Unity/0.0.0.0": {"runtime": {"Il2CppRG.ImGui.Unity.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.OdinInspector.Attributes/*******": {"runtime": {"Il2CppSirenix.OdinInspector.Attributes.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.OdinInspector.Modules.Unity.Addressables/0.0.0.0": {"runtime": {"Il2CppSirenix.OdinInspector.Modules.Unity.Addressables.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.OdinInspector.Modules.UnityLocalization/0.0.0.0": {"runtime": {"Il2CppSirenix.OdinInspector.Modules.UnityLocalization.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.Serialization.AOTGenerated/0.0.0.0": {"runtime": {"Il2CppSirenix.Serialization.AOTGenerated.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.Serialization.Config/*******": {"runtime": {"Il2CppSirenix.Serialization.Config.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.Serialization/*******": {"runtime": {"Il2CppSirenix.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSirenix.Utilities/*******": {"runtime": {"Il2CppSirenix.Utilities.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppStreamChat.Core/0.0.0.0": {"runtime": {"Il2CppStreamChat.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppStreamChat.Libs/0.0.0.0": {"runtime": {"Il2CppStreamChat.Libs.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Configuration/*******": {"runtime": {"Il2CppSystem.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Core/*******": {"runtime": {"Il2CppSystem.Core.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Data/*******": {"runtime": {"Il2CppSystem.Data.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem/*******": {"runtime": {"Il2CppSystem.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Drawing/*******": {"runtime": {"Il2CppSystem.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.IO.Compression/*******": {"runtime": {"Il2CppSystem.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.IO.Compression.FileSystem/*******": {"runtime": {"Il2CppSystem.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Net.Http/*******": {"runtime": {"Il2CppSystem.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Numerics/*******": {"runtime": {"Il2CppSystem.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Runtime.CompilerServices.Unsafe/*******": {"runtime": {"Il2CppSystem.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Runtime.Serialization/*******": {"runtime": {"Il2CppSystem.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Xml/*******": {"runtime": {"Il2CppSystem.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppSystem.Xml.Linq/*******": {"runtime": {"Il2CppSystem.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppTriangleNET/*******": {"runtime": {"Il2CppTriangleNET.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Il2CppUMA_Content/0.0.0.0": {"runtime": {"Il2CppUMA_Content.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppUMA_Core/0.0.0.0": {"runtime": {"Il2CppUMA_Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppUMA_Examples/0.0.0.0": {"runtime": {"Il2CppUMA_Examples.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppUniTask.Addressables/0.0.0.0": {"runtime": {"Il2CppUniTask.Addressables.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppUniTask/0.0.0.0": {"runtime": {"Il2CppUniTask.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2CppXNode/0.0.0.0": {"runtime": {"Il2CppXNode.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Il2Cpp__Generated/0.0.0.0": {"runtime": {"Il2Cpp__Generated.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "IndexRange/*******": {"runtime": {"IndexRange.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MelonLoader/*******": {"runtime": {"MelonLoader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MelonLoader.NativeHost/*******": {"runtime": {"MelonLoader.NativeHost.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Bcl.AsyncInterfaces/*******": {"runtime": {"Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"runtime": {"Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.8.10101", "fileVersion": "0.2.8.10101"}}}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"runtime": {"Microsoft.Diagnostics.Runtime.dll": {"assemblyVersion": "3.1.10.12801", "fileVersion": "3.1.10.12801"}}}, "Microsoft.Extensions.Configuration.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Binder/*******": {"runtime": {"Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration/*******": {"runtime": {"Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/*******": {"runtime": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Logging.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.Logging/*******": {"runtime": {"Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options/*******": {"runtime": {"Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Primitives/*******": {"runtime": {"Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Mono.Cecil/********": {"runtime": {"Mono.Cecil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Mdb/********": {"runtime": {"Mono.Cecil.Mdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Pdb/********": {"runtime": {"Mono.Cecil.Pdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Rocks/********": {"runtime": {"Mono.Cecil.Rocks.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MonoMod.Backports/*******": {"runtime": {"MonoMod.Backports.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MonoMod/*********": {"runtime": {"MonoMod.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "MonoMod.ILHelpers/*******": {"runtime": {"MonoMod.ILHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "MonoMod.RuntimeDetour/*********": {"runtime": {"MonoMod.RuntimeDetour.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "MonoMod.Utils/*********": {"runtime": {"MonoMod.Utils.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Newtonsoft.Json/1*******": {"runtime": {"Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "System.Configuration.ConfigurationManager/*******": {"runtime": {"System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/*******": {"runtime": {"System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Permissions/*******": {"runtime": {"System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Windows.Extensions/*******": {"runtime": {"System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Tomlet/*******": {"runtime": {"Tomlet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Unity.Addressables/0.0.0.0": {"runtime": {"Unity.Addressables.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.AI.Navigation/0.0.0.0": {"runtime": {"Unity.AI.Navigation.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Burst/0.0.0.0": {"runtime": {"Unity.Burst.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Burst.Unsafe/*******": {"runtime": {"Unity.Burst.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "Unity.Collections/0.0.0.0": {"runtime": {"Unity.Collections.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Collections.LowLevel.ILSupport/0.0.0.0": {"runtime": {"Unity.Collections.LowLevel.ILSupport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Localization/0.0.0.0": {"runtime": {"Unity.Localization.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Mathematics/0.0.0.0": {"runtime": {"Unity.Mathematics.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.MemoryProfiler/0.0.0.0": {"runtime": {"Unity.MemoryProfiler.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Postprocessing.Runtime/0.0.0.0": {"runtime": {"Unity.Postprocessing.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Profiling.Core/0.0.0.0": {"runtime": {"Unity.Profiling.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.RenderPipelines.Core.Runtime/0.0.0.0": {"runtime": {"Unity.RenderPipelines.Core.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.ResourceManager/0.0.0.0": {"runtime": {"Unity.ResourceManager.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Analytics/0.0.0.0": {"runtime": {"Unity.Services.Analytics.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Configuration/0.0.0.0": {"runtime": {"Unity.Services.Core.Configuration.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Device/0.0.0.0": {"runtime": {"Unity.Services.Core.Device.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core/0.0.0.0": {"runtime": {"Unity.Services.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Environments/0.0.0.0": {"runtime": {"Unity.Services.Core.Environments.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Environments.Internal/0.0.0.0": {"runtime": {"Unity.Services.Core.Environments.Internal.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Internal/0.0.0.0": {"runtime": {"Unity.Services.Core.Internal.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Registration/0.0.0.0": {"runtime": {"Unity.Services.Core.Registration.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Scheduler/0.0.0.0": {"runtime": {"Unity.Services.Core.Scheduler.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Telemetry/0.0.0.0": {"runtime": {"Unity.Services.Core.Telemetry.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Services.Core.Threading/0.0.0.0": {"runtime": {"Unity.Services.Core.Threading.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.TextMeshPro/0.0.0.0": {"runtime": {"Unity.TextMeshPro.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Unity.Timeline/*******": {"runtime": {"Unity.Timeline.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AccessibilityModule/0.0.0.0": {"runtime": {"UnityEngine.AccessibilityModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AIModule/0.0.0.0": {"runtime": {"UnityEngine.AIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AndroidJNIModule/0.0.0.0": {"runtime": {"UnityEngine.AndroidJNIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AnimationModule/0.0.0.0": {"runtime": {"UnityEngine.AnimationModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ARModule/0.0.0.0": {"runtime": {"UnityEngine.ARModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AssetBundleModule/0.0.0.0": {"runtime": {"UnityEngine.AssetBundleModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.AudioModule/0.0.0.0": {"runtime": {"UnityEngine.AudioModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ClothModule/0.0.0.0": {"runtime": {"UnityEngine.ClothModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ContentLoadModule/0.0.0.0": {"runtime": {"UnityEngine.ContentLoadModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.CoreModule/0.0.0.0": {"runtime": {"UnityEngine.CoreModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.CrashReportingModule/0.0.0.0": {"runtime": {"UnityEngine.CrashReportingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.DirectorModule/0.0.0.0": {"runtime": {"UnityEngine.DirectorModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine/0.0.0.0": {"runtime": {"UnityEngine.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.DSPGraphModule/0.0.0.0": {"runtime": {"UnityEngine.DSPGraphModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.GameCenterModule/0.0.0.0": {"runtime": {"UnityEngine.GameCenterModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.GIModule/0.0.0.0": {"runtime": {"UnityEngine.GIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.GraphicsStateCollectionSerializerModule/0.0.0.0": {"runtime": {"UnityEngine.GraphicsStateCollectionSerializerModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.GridModule/0.0.0.0": {"runtime": {"UnityEngine.GridModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.HierarchyCoreModule/0.0.0.0": {"runtime": {"UnityEngine.HierarchyCoreModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.HotReloadModule/0.0.0.0": {"runtime": {"UnityEngine.HotReloadModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Il2CppAssetBundleManager/*******": {"runtime": {"UnityEngine.Il2CppAssetBundleManager.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "UnityEngine.Il2CppImageConversionManager/*******": {"runtime": {"UnityEngine.Il2CppImageConversionManager.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "UnityEngine.ImageConversionModule/0.0.0.0": {"runtime": {"UnityEngine.ImageConversionModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.IMGUIModule/0.0.0.0": {"runtime": {"UnityEngine.IMGUIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputForUIModule/0.0.0.0": {"runtime": {"UnityEngine.InputForUIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputLegacyModule/0.0.0.0": {"runtime": {"UnityEngine.InputLegacyModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.InputModule/0.0.0.0": {"runtime": {"UnityEngine.InputModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.JSONSerializeModule/0.0.0.0": {"runtime": {"UnityEngine.JSONSerializeModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.LocalizationModule/0.0.0.0": {"runtime": {"UnityEngine.LocalizationModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.MarshallingModule/0.0.0.0": {"runtime": {"UnityEngine.MarshallingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.MultiplayerModule/0.0.0.0": {"runtime": {"UnityEngine.MultiplayerModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ParticleSystemModule/0.0.0.0": {"runtime": {"UnityEngine.ParticleSystemModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.PerformanceReportingModule/0.0.0.0": {"runtime": {"UnityEngine.PerformanceReportingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.Physics2DModule/0.0.0.0": {"runtime": {"UnityEngine.Physics2DModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.PhysicsModule/0.0.0.0": {"runtime": {"UnityEngine.PhysicsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.PropertiesModule/0.0.0.0": {"runtime": {"UnityEngine.PropertiesModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule/0.0.0.0": {"runtime": {"UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ScreenCaptureModule/0.0.0.0": {"runtime": {"UnityEngine.ScreenCaptureModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.ShaderVariantAnalyticsModule/0.0.0.0": {"runtime": {"UnityEngine.ShaderVariantAnalyticsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SharedInternalsModule/0.0.0.0": {"runtime": {"UnityEngine.SharedInternalsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SpriteMaskModule/0.0.0.0": {"runtime": {"UnityEngine.SpriteMaskModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SpriteShapeModule/0.0.0.0": {"runtime": {"UnityEngine.SpriteShapeModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.StreamingModule/0.0.0.0": {"runtime": {"UnityEngine.StreamingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SubstanceModule/0.0.0.0": {"runtime": {"UnityEngine.SubstanceModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.SubsystemsModule/0.0.0.0": {"runtime": {"UnityEngine.SubsystemsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TerrainModule/0.0.0.0": {"runtime": {"UnityEngine.TerrainModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TerrainPhysicsModule/0.0.0.0": {"runtime": {"UnityEngine.TerrainPhysicsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextCoreFontEngineModule/0.0.0.0": {"runtime": {"UnityEngine.TextCoreFontEngineModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextCoreTextEngineModule/0.0.0.0": {"runtime": {"UnityEngine.TextCoreTextEngineModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TextRenderingModule/0.0.0.0": {"runtime": {"UnityEngine.TextRenderingModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TilemapModule/0.0.0.0": {"runtime": {"UnityEngine.TilemapModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.TLSModule/0.0.0.0": {"runtime": {"UnityEngine.TLSModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UI/*******": {"runtime": {"UnityEngine.UI.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UIElementsModule/0.0.0.0": {"runtime": {"UnityEngine.UIElementsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UIModule/0.0.0.0": {"runtime": {"UnityEngine.UIModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UmbraModule/0.0.0.0": {"runtime": {"UnityEngine.UmbraModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityAnalyticsCommonModule/0.0.0.0": {"runtime": {"UnityEngine.UnityAnalyticsCommonModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityAnalyticsModule/0.0.0.0": {"runtime": {"UnityEngine.UnityAnalyticsModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityConnectModule/0.0.0.0": {"runtime": {"UnityEngine.UnityConnectModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityCurlModule/0.0.0.0": {"runtime": {"UnityEngine.UnityCurlModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityTestProtocolModule/0.0.0.0": {"runtime": {"UnityEngine.UnityTestProtocolModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestAssetBundleModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestAssetBundleModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestAudioModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestAudioModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestTextureModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestTextureModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.UnityWebRequestWWWModule/0.0.0.0": {"runtime": {"UnityEngine.UnityWebRequestWWWModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.VehiclesModule/0.0.0.0": {"runtime": {"UnityEngine.VehiclesModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.VFXModule/0.0.0.0": {"runtime": {"UnityEngine.VFXModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.VideoModule/0.0.0.0": {"runtime": {"UnityEngine.VideoModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.VRModule/0.0.0.0": {"runtime": {"UnityEngine.VRModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.WindModule/0.0.0.0": {"runtime": {"UnityEngine.WindModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "UnityEngine.XRModule/0.0.0.0": {"runtime": {"UnityEngine.XRModule.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "WebSocketDotNet/*******": {"runtime": {"WebSocketDotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Il2CppEntroPI.CloudShadows.Runtime/0.0.0.0": {"runtime": {"Il2CppEntroPI.CloudShadows.Runtime.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"TestLE/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MoonSharp/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uiAcRh7d+53k3xW9pFDJfAFVw4RnjHVCJG05M3oPAVEVwPtFavhg1H/IpC6So4X1j9kJlzuLlA3OghhPcIvc5A==", "path": "moonsharp/2.0.0", "hashPath": "moonsharp.2.0.0.nupkg.sha512"}, "0Harmony/********": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.DotNet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AsmResolver.PE.File/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetRipper.Primitives/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "bHapticsLib/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Iced/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2Cpp/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppALINE/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppBezierSolution.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppCinemachine/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppCommandLine/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppCrest/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2Cppcrosstales/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppCSharp.OperationResult/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppDialogueSystem/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppDOTween/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppDOTweenPro/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppDunGen/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppEHRGB/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppFacepunch.Steamworks.Win64/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppFmodShared/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppFMODUnity/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppFMODUnityResonance/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppGenerated/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppHeathen.ScreenKeyboard/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppHoudiniEngineUnity/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Generator/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.HarmonySupport/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppInterop.Runtime/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppJBooth.MicroSplat.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.Networking.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.PCG/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.Telemetry.Client/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.Telemetry/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLE.UI.Controls/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLidgren.Network/2012.1.7.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppLuaInterpreter/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppMathNet.Numerics/4.15.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppMeshExtension/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppMono.Security/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2Cppmscorlib/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNewAssembly/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNewtonsoft.Json/1*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppNewtonsoft.Json.UnityConverters/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppPlayFab/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppPolly/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppProBuilderCore-Unity5/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppProBuilderMeshOps-Unity5/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppRewired_Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppRewired_Windows/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppRewired_Windows_Functions/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppRG.ImGui/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppRG.ImGui.Unity/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.OdinInspector.Attributes/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.OdinInspector.Modules.Unity.Addressables/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.OdinInspector.Modules.UnityLocalization/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.Serialization.AOTGenerated/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.Serialization.Config/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.Serialization/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSirenix.Utilities/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppStreamChat.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppStreamChat.Libs/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Data/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Drawing/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.IO.Compression/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.IO.Compression.FileSystem/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Net.Http/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Numerics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Runtime.CompilerServices.Unsafe/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Runtime.Serialization/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Xml/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppSystem.Xml.Linq/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppTriangleNET/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUMA_Content/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUMA_Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUMA_Examples/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUniTask.Addressables/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppUniTask/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppXNode/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2Cpp__Generated/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "IndexRange/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MelonLoader/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MelonLoader.NativeHost/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Bcl.AsyncInterfaces/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.NETCore.Client/0.2.8.10101": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Diagnostics.Runtime/3.1.10.12801": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Rocks/********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Backports/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.ILHelpers/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.RuntimeDetour/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "MonoMod.Utils/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/1*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Configuration.ConfigurationManager/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.ProtectedData/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security.Permissions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Windows.Extensions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Tomlet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Addressables/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.AI.Navigation/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Burst/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Burst.Unsafe/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Collections/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Collections.LowLevel.ILSupport/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Localization/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Mathematics/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.MemoryProfiler/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Postprocessing.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Profiling.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.RenderPipelines.Core.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.ResourceManager/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Analytics/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Configuration/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Device/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Environments/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Environments.Internal/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Internal/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Registration/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Scheduler/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Telemetry/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Services.Core.Threading/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.TextMeshPro/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Unity.Timeline/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AccessibilityModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AndroidJNIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AnimationModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ARModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AssetBundleModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.AudioModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ClothModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ContentLoadModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.CoreModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.CrashReportingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.DirectorModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.DSPGraphModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.GameCenterModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.GIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.GraphicsStateCollectionSerializerModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.GridModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.HierarchyCoreModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.HotReloadModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Il2CppAssetBundleManager/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Il2CppImageConversionManager/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ImageConversionModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.IMGUIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputForUIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputLegacyModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.InputModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.JSONSerializeModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.LocalizationModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.MarshallingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.MultiplayerModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ParticleSystemModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.PerformanceReportingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.Physics2DModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.PhysicsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.PropertiesModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ScreenCaptureModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.ShaderVariantAnalyticsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SharedInternalsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SpriteMaskModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SpriteShapeModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.StreamingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SubstanceModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.SubsystemsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TerrainModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TerrainPhysicsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextCoreFontEngineModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextCoreTextEngineModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TextRenderingModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TilemapModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.TLSModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UI/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UIElementsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UIModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UmbraModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityAnalyticsCommonModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityAnalyticsModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityConnectModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityCurlModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityTestProtocolModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestAssetBundleModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestAudioModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestTextureModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.UnityWebRequestWWWModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.VehiclesModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.VFXModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.VideoModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.VRModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.WindModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "UnityEngine.XRModule/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "WebSocketDotNet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Il2CppEntroPI.CloudShadows.Runtime/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}