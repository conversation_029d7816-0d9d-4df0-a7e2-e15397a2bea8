using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Manages all combat logic.
/// Follows SRP by focusing only on combat-related operations.
/// </summary>
public class HandleCombatTask : IGameTask
{
    private readonly ICombatService _combatService;
    private readonly IGameStateService _gameStateService;
    private readonly Dictionary<Enemy, int> _enemyInactiveFailSafe = new();
    
    private Enemy? _targetEnemy;
    private float _distance;

    public HandleCombatTask(ICombatService combatService, IGameStateService gameStateService)
    {
        _combatService = combatService;
        _gameStateService = gameStateService;
    }

    public bool CanExecute()
    {
        // Find nearest enemy
        var (enemy, distance) = _combatService.FindNearestEnemy(PLAYER.transform.position, 100);
        if (enemy == null) 
            return false;

        _targetEnemy = enemy;
        _distance = distance;

        // Check if we are in combat range, if so, do combat routine
        if (distance <= CURRENT_ROUTINE!.CombatDistance)
            return true;

        // If no objectives, default to combat
        return !_gameStateService.HasObjectives();
    }

    public IEnumerator Execute()
    {
        // Handle inactive enemies that are too close
        if (_distance <= 3f && _targetEnemy!.Data.Data.actorName != "Exiled Mage" && 
            (!_targetEnemy.Data.actorSync.gameObject.active || !_targetEnemy.Data.isActiveAndEnabled))
        {
            MelonLogger.Msg("Enemy is too close and not active!");
            _targetEnemy.RemoveEnemy();
            yield break;
        }
        
        if (_targetEnemy == null)
        {
            MelonLogger.Msg("Target enemy is null!");
            yield break;
        }

        var enemyTransform = _targetEnemy.Data.transform;
        if (enemyTransform == null)
        {
            MelonLogger.Msg("Enemy transform is null!");
            yield break;
        }

        if (_targetEnemy.Data.gameObject.active)
        {
            yield return _combatService.ExecuteCombatRoutine(_targetEnemy, enemyTransform, _distance);
        }
        else
        {
            if (_distance <= 3f)
            {
                _enemyInactiveFailSafe[_targetEnemy] = _enemyInactiveFailSafe.GetValueOrDefault(_targetEnemy) + 1;
                if (_enemyInactiveFailSafe[_targetEnemy] >= 10)
                {
                    MelonLogger.Msg("Enemy is inactive for 10 tries, removing enemy!");
                    _targetEnemy.RemoveEnemy();
                    _enemyInactiveFailSafe.Remove(_targetEnemy);
                }
            }

            PlayerHelpers.MoveTo(enemyTransform.position);
            yield return new WaitForSeconds(0.3333f);
        }
    }
}
