﻿using MelonLoader;
using TestLE.Routine;
using TestLE.Routine.CombatRoutines;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE;

public class Test : MelonMod
{
    private MelonPreferences_Category CombatCategory = null!;
    private MelonPreferences_Entry<int> CombatRoutineEntry = null!;

    public readonly List<CombatRoutine> CombatRoutines = new()
    {
        new Beastmaster_Kripp(),
        new Necromancer_Mattjestic()
    };

    private readonly List<Patch> PatchInstances = new();

    public override void OnInitializeMelon()
    {
        CombatCategory = MelonPreferences.CreateCategory("Combat");
        CombatRoutineEntry = CombatCategory.CreateEntry("CombatRoutineEntry", 0);

        if (CombatRoutineEntry.Value >= 0 && CombatRoutineEntry.Value < CombatRoutines.Count)
            CURRENT_ROUTINE = CombatRoutines[CombatRoutineEntry.Value];
        else
            CURRENT_ROUTINE = CombatRoutines.FirstOrDefault();

        // Reflection: Find all non-abstract subclasses of TestLE.Patch, instantiate, add to list, call Setup
        try
        {
            var patchType = typeof(Patch);
            var assembly = patchType.Assembly;
            var patchTypes = assembly.GetTypes()
                .Where(t => t.IsSubclassOf(patchType) && !t.IsAbstract && t.GetConstructor(Type.EmptyTypes) != null)
                .ToList();

            foreach (var type in patchTypes)
            {
                try
                {
                    var instance = (Patch)Activator.CreateInstance(type)!;
                    PatchInstances.Add(instance);
                    try
                    {
                        instance.Setup();
                    }
                    catch (Exception ex)
                    {
                        MelonLogger.Error($"Error calling Setup on {type.FullName}: {ex}");
                    }
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error instantiating {type.FullName}: {ex}");
                }
            }
            MelonLogger.Msg($"Loaded {PatchInstances.Count} Patch subclasses.");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Reflection error loading Patch subclasses: {ex}");
        }
    }

    public override async void OnSceneWasInitialized(int buildIndex, string sceneName)
    {
        MelonLogger.Msg($"Scene initialized: {sceneName}");

        // PLAYER = null!;
        CURRENT_SCENE = sceneName;
        ResetGlobals();

        // if (sceneName is "ClientSplash" or "PersistentUI" or "Login" or "CharacterSelectScene" or "")
        //     return;
        //
        // while (PLAYER == null && CURRENT_SCENE == sceneName) //TODO: Hook player initialized instead
        // {
        //     PLAYER = GameObject.FindGameObjectWithTag("Player")?.GetComponent<LocalPlayer>()!;
        //     if (PLAYER == null)
        //     {
        //         MelonLogger.Msg($"Player not found, waiting... (SceneName: {CURRENT_SCENE})");
        //         await Task.Delay(1000);
        //     }
        //     else
        //     {
        //         MelonLogger.Msg("Player found!");
        //         Signals.OnPlayerInitialized.Trigger();
        //     }
        // }
    }

    public override void OnUpdate()
    {
        // if (Input.GetKeyDown(KeyCode.F7)) 
        //     CURRENT_ROUTINE = CURRENT_ROUTINE != null ? CombatRoutines[(CombatRoutines.IndexOf(CURRENT_ROUTINE) + 1) % CombatRoutines.Count] : CombatRoutines.FirstOrDefault();

        if (Input.GetKeyDown(KeyCode.PageDown))
        {
            // Switch to the next combat routine
            CURRENT_ROUTINE = CURRENT_ROUTINE != null ? CombatRoutines[(CombatRoutines.IndexOf(CURRENT_ROUTINE) + 1) % CombatRoutines.Count] : CombatRoutines.FirstOrDefault();
            CombatRoutineEntry.Value = CombatRoutines.IndexOf(CURRENT_ROUTINE!);
            MelonPreferences.Save();
        }

        if (Input.GetKeyDown(KeyCode.PageUp))
        {
            // Switch to the previous combat routine
            CURRENT_ROUTINE = CURRENT_ROUTINE != null ? CombatRoutines[(CombatRoutines.IndexOf(CURRENT_ROUTINE) - 1 + CombatRoutines.Count) % CombatRoutines.Count] : CombatRoutines.LastOrDefault();
            CombatRoutineEntry.Value = CombatRoutines.IndexOf(CURRENT_ROUTINE!);
            MelonPreferences.Save();
        }

        if (Input.GetKeyDown(KeyCode.F8))
            SHOW_UI = !SHOW_UI;

        if (PLAYER == null)
            return;

        if (CURRENT_ROUTINE is { PotionHealthUse: > 0f } && PLAYER.playerHealth.currentHealth / PLAYER.playerHealth.maxHealth <= CURRENT_ROUTINE.PotionHealthUse) // TODO: Move to routine
            PlayerHelpers.UsePotion();

        if (Input.GetKeyDown(KeyCode.Mouse4))
        {
            MelonLogger.Msg("Mouse 4 pressed.");
            MelonCoroutines.Start(Input.GetKey(KeyCode.LeftShift) ? AUCTION_HOUSE_UI.ListHoveredItem() : AUCTION_HOUSE_UI.SearchHoveredItem());
        }
    }

    public override void OnGUI()
    {
        if (PLAYER == null || !SHOW_UI)
            return;

        if (MAIN_ROUTINE_COROUTINE != null)
        {
            if (GUI.Button(new Rect(10, 10, 50, 20), "Stop"))
            {
                MelonCoroutines.Stop(MAIN_ROUTINE_COROUTINE);
                MAIN_ROUTINE_COROUTINE = null;
            }
        }
        else
        {
            if (GUI.Button(new Rect(10, 10, 50, 20), "Start"))
            {
                MAIN_ROUTINE = new MainRoutine();
                MAIN_ROUTINE_COROUTINE = MelonCoroutines.Start(MAIN_ROUTINE);
            }
        }

        GUI.Label(new Rect(10, 30, 200, 20), $"Routine: {(CURRENT_ROUTINE != null ? CURRENT_ROUTINE.GetType().Name : "None")}");

        GUI.BeginGroup(new Rect(10, 50, Screen.width - 20, Screen.height - 60));
        AUCTION_HOUSE_UI.DrawUI();
        GUI.EndGroup();
    }
}


// PLAYER.movingState.MouseClickMoveCommand(CAMERA.ScreenPointToRay(CAMERA.WorldToScreenPoint(enemyTransform.position)), false, -1.0f, false, Vector3.zero, true);

// if (PLAYER.usingAbilityState.abilityList.getAbility(1).abilityName is not "Crowstorm")
// {
//     PlayerHelpers.UseAbility(1, enemyTransform);
// }
// else if (distance is >= 5 and <= 20 && !PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 3))
// {
//     PlayerHelpers.UseAbility(3, enemyTransform);
// }
// else if (distance <= 5 && !PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 2))
// {
//     PlayerHelpers.UseAbility(2, enemyTransform);
// }
// else if (!PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 0))
// {
//     PlayerHelpers.UseAbility(0, enemyTransform);
// }
// else
// {
//     PlayerHelpers.UseAbility(4, enemyTransform);
// }
//
// yield return new WaitForSeconds(0.1f);


// private IEnumerator CombatRoutine()
// {
//     if (PLAYER == null)
//     {
//         MelonLogger.Msg("Player is null!");
//         yield break;
//     }
//
//     while (_doingCombatRoutine)
//     {
//         // yield return MelonCoroutines.Start(CheckMonolithCompleted());
//
//         var enemy = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
//         if (enemy == null)
//         {
//             MelonLogger.Msg("No mobs found!");
//             yield return new WaitForSeconds(1f);
//             continue;
//         }
//
//         var distance = Vector3.Distance(PLAYER.transform.position, enemy.data.transform.position);
//         if (distance <= 0.5f && !enemy.data.isActiveAndEnabled)
//         {
//             MelonLogger.Msg("Enemy is too close and not active!");
//             enemy.RemoveEnemy();
//             continue;
//         }
//
//         var enemyTransform = enemy.data.transform;
//         if (enemy.data.gameObject.active && distance <= 30)
//         {
//             if (PLAYER.usingAbilityState.abilityList.getAbility(1).abilityName is not "Crowstorm")
//             {
//                 PlayerHelpers.UseAbility(1, enemyTransform);
//             }
//             else if (distance is >= 5 and <= 20 && !PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 3))
//             {
//                 PlayerHelpers.UseAbility(3, enemyTransform);
//             }
//             else if (distance <= 5 && !PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 2))
//             {
//                 PlayerHelpers.UseAbility(2, enemyTransform);
//             }
//             else if (!PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, 0))
//             {
//                 PlayerHelpers.UseAbility(0, enemyTransform);
//             }
//             else
//             {
//                 PlayerHelpers.UseAbility(4, enemyTransform);
//             }
//
//             yield return new WaitForSeconds(0.1f);
//         }
//         else
//         {
//             PlayerHelpers.MoveTo(enemyTransform.position);
//             yield return new WaitForSeconds(0.3333f);
//         }
//     }
// }
//
// private IEnumerator CheckMonolithCompleted()
// {
//     var button = FindHelpers.FindMonolithCompleteButton();
//     if (button == null)
//         yield break;
//
//     PlayerHelpers.UsePortal();
//     yield return new WaitForSeconds(1f);
//
//     var portal = FindHelpers.FindMonolithPortal();
//     if (portal == null)
//     {
//         MelonLogger.Msg("Portal not found!");
//         yield break;
//     }
//
//     portal.ObjectClick(PLAYER!.gameObject, true);
//     yield return new WaitForSeconds(1f);
//
//     var chest = FindHelpers.FindMonolithCompleteRewardChest();
//     if (chest.obj == null)
//     {
//         MelonLogger.Msg("Chest not found!");
//         yield break;
//     }
//
//     chest.obj.ObjectClick(PLAYER.gameObject, true);
//     yield return new WaitForSeconds(1f);
//
//     var rock = FindHelpers.FindMonolithCompleteRewardRock();
//     if (rock.obj == null)
//     {
//         MelonLogger.Msg("Rock not found!");
//         yield break;
//     }
//
//     rock.obj.ObjectClick(PLAYER.gameObject, true);
//     yield return new WaitForSeconds(1f);
//
//     MelonLogger.Msg("Monolith completed!");
//     _doingCombatRoutine = false;
// }

//


// Player.movingState.MouseClickMoveCommand(Camera.main.ScreenPointToRay(Input.mousePosition), false, -1.0f, false, Vector3.zero, true);
// MelonLogger.Msg($"Mouse click move command sent!, Ray: {Camera.main.ScreenPointToRay(Input.mousePosition)}");


// Debug.DrawLine(Player.transform.position, Player.transform.position + new Vector3(0, 10, 0), Color.red, 0.1f);

// if (Input.GetKeyDown(KeyCode.T)) // test
// {
//     MelonLogger.Msg($"Health: {Player.playerHealth.currentHealth.ToString(CultureInfo.InvariantCulture)}");
//
//     Player.PotionKeyPressed();
//     foreach (var a in Player.abilityList.abilities)
//     {
//         MelonLogger.Msg($"Ability: {a.name}");
//     }
// }

// Pathfinder.CalculatePath(PLAYER.movingState.myAgent, sceneTransitioners[0].transform.parent.transform);


// if (Player.playerHealth.currentHealth <= 60)
// {
//     Player.PotionKeyPressed();
//     MelonLogger.Msg("Potion used!");
// }


// public override void OnInitializeMelon()
// {
//     Signals.s_onPlayerInitialized.Subscribe((player) =>
//     {
//         player.GetComponent<PlayerHealth>().healthChangeEvent += new Action(prut);
//         player.GetComponent<PlayerHealth>().damageTakenEvent += new Action<float, float>(damageprut);
//         MelonLogger.Msg("fisseeeee!");
//     });
// }

// public void prut()
// {
//     MelonLogger.Msg("prut");
//
//     if (Player == null)
//         return;
//
//     MelonLogger.Msg(Player.GetComponent<PlayerHealth>().currentHealth.ToString(CultureInfo.InvariantCulture));
// }
//
// public void damageprut(float flatDamage, float damagePercent)
// {
//     MelonLogger.Msg("damageprut");
//
//     if (Player == null)
//         return;
//
//     MelonLogger.Msg($"Damage: {flatDamage.ToString(CultureInfo.InvariantCulture)} + {damagePercent.ToString(CultureInfo.InvariantCulture)}%");
// }


// using UnityEngine;
// using UnityEngine.AI;
//
// public class PathFinder : MonoBehaviour
// {
//     public Transform target; // The target object
//     private NavMeshPath path; // The calculated path
//     private NavMeshAgent agent; // The NavMeshAgent component
//
//     void Start()
//     {
//         agent = GetComponent<NavMeshAgent>();
//         path = new NavMeshPath();
//     }
//
//     void Update()
//     {
//         if (target != null)
//         {
//             // Calculate the path
//             if (agent.CalculatePath(target.position, path))
//             {
//                 // The path is calculated and stored in the 'path' variable
//                 // You can use 'path.corners' to access the waypoints of the path
//             }
//         }
//     }
// }


// using UnityEngine;
// using UnityEngine.AI;
//
// public class PathVisualizer : MonoBehaviour
// {
//     public Transform target; // The target object
//     private NavMeshPath path; // The calculated path
//     private NavMeshAgent agent; // The NavMeshAgent component
//     private LineRenderer lineRenderer; // The LineRenderer component
//
//     void Start()
//     {
//         agent = GetComponent<NavMeshAgent>();
//         path = new NavMeshPath();
//         lineRenderer = gameObject.AddComponent<LineRenderer>();
//     }
//
//     void Update()
//     {
//         if (target != null)
//         {
//             // Calculate the path
//             if (agent.CalculatePath(target.position, path))
//             {
//                 // Set the positions of the LineRenderer to the corners of the path
//                 lineRenderer.positionCount = path.corners.Length;
//                 lineRenderer.SetPositions(path.corners);
//             }
//         }
//     }
// }
