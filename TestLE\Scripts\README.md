# TestLE Scripting System

The TestLE mod includes a powerful Lua scripting system that allows you to create custom routines and behaviors that integrate seamlessly with the existing task-based architecture.

## Overview

The scripting system provides:
- **Lua Script Execution**: Run custom Lua scripts as part of the bot routine
- **Game API Access**: Full access to game objects, player data, and utilities
- **Hot Reload**: Scripts are automatically reloaded when files change
- **Priority System**: Scripts can be prioritized alongside built-in tasks
- **Configuration**: Per-script settings and parameters
- **Error Handling**: Robust error handling and logging

## Getting Started

### Script Structure

Every script should follow this basic structure:

```lua
-- Script metadata (optional but recommended)
SCRIPT_NAME = "My Custom Script"
SCRIPT_DESCRIPTION = "Description of what this script does"
SCRIPT_VERSION = "1.0.0"
SCRIPT_AUTHOR = "Your Name"
SCRIPT_PRIORITY = 100  -- Higher numbers = higher priority

-- Check if script should execute
function CanExecute()
    -- Return true if this script should run
    return true
end

-- Main execution function
function Execute()
    -- Your script logic here
    Log("<PERSON>rip<PERSON> is running!")
    return true
end

-- Optional: Called when script loads
function OnLoad()
    Log("Script loaded!")
end

-- Optional: Called when script unloads
function OnUnload()
    Log("Script unloaded!")
end
```

### Required Functions

- **CanExecute()**: Must return `true` if the script should execute, `false` otherwise
- **Execute()**: Contains the main script logic

### Optional Functions

- **OnLoad()**: Called when the script is first loaded
- **OnUnload()**: Called when the script is unloaded or reloaded

## Available APIs

### Game Objects

```lua
-- Get core game objects
local player = GetPlayer()
local enemies = GetEnemies()
local groundItems = GetGroundItems()
local interactables = GetInteractables()
local objectives = GetMonolithObjectives()
local shrines = GetGoodShrines()

-- Access through Game object
local player = Game.Player
local enemies = Game.Enemies
```

### Player API

```lua
-- Player information
local position = Player.GetPosition()
local health = Player.GetHealth()
local maxHealth = Player.GetMaxHealth()
local healthPercent = Player.GetHealthPercent()
local isAlive = Player.IsAlive()
local inCombat = Player.IsInCombat()
```

### Math API

```lua
-- Distance calculations
local distance = Math.Distance(pos1, pos2)
local distance2D = Math.Distance2D(pos1, pos2)

-- Vector operations
local magnitude = Math.Magnitude(vector)
local normalized = Math.Normalize(vector)
local dot = Math.Dot(vec1, vec2)
local cross = Math.Cross(vec1, vec2)

-- Utility functions
local clamped = Math.Clamp(value, min, max)
local randomValue = Math.Random()
local randomRange = Math.RandomRange(min, max)
```

### Find API

```lua
-- Find nearby objects
local nearestEnemy = Find.FindNearestEnemy(position, maxDistance)
local nearestItem = Find.FindNearestGroundItem(position, maxDistance)
local nearestInteractable = Find.FindNearestInteractable(position, maxDistance)
```

### Unity API

```lua
-- Create vectors
local vector = Unity.CreateVector3(x, y, z)
local vector2D = Unity.CreateVector2(x, y)

-- Input
local keyPressed = Unity.GetKey("F1")
local keyDown = Unity.GetKeyDown("Space")
local leftMouse = Unity.LeftMouseButton

-- Time
local currentTime = Unity.Time
local deltaTime = Unity.DeltaTime
```

### Logging

```lua
-- Logging functions
Log("Information message")
LogWarning("Warning message")
LogError("Error message")
```

### Utility Functions

```lua
-- Wait functions (for coroutines)
Wait(2.5)  -- Wait 2.5 seconds
WaitFrame()  -- Wait one frame

-- Distance helpers
local distance = Distance(pos1, pos2)
local distance2D = Distance2D(pos1, pos2)
```

## Configuration

Scripts can be configured through the `config.json` file:

```json
{
  "ScriptSettings": {
    "MyScript": {
      "Enabled": true,
      "Priority": 150,
      "Parameters": {
        "CustomSetting": "value",
        "NumberSetting": 42
      }
    }
  }
}
```

## Examples

See the included example scripts:
- `ExampleBasicScript.lua` - Basic script structure and API usage
- `ExampleCombatScript.lua` - Combat detection and enemy analysis
- `ExampleLootScript.lua` - Loot monitoring and item analysis

## Best Practices

1. **Use descriptive script metadata** - Include name, description, version, and author
2. **Handle errors gracefully** - Use proper error checking and logging
3. **Respect the priority system** - Set appropriate priorities for your scripts
4. **Keep CanExecute() fast** - This function is called frequently
5. **Use logging effectively** - Log important events but avoid spam
6. **Test thoroughly** - Test scripts in various game situations

## Troubleshooting

### Script Not Loading
- Check the console for error messages
- Ensure the script file has a `.lua` extension
- Verify the script syntax is correct

### Script Not Executing
- Check that `CanExecute()` returns `true`
- Verify the script is enabled in `config.json`
- Check the script priority relative to other tasks

### Performance Issues
- Avoid expensive operations in `CanExecute()`
- Use appropriate check intervals
- Profile your script execution time

## Advanced Features

### Hot Reload
Scripts are automatically reloaded when files change. This allows for rapid development and testing.

### Priority System
Scripts are executed based on priority alongside built-in tasks. Higher priority numbers execute first.

### Error Recovery
The scripting system includes robust error handling to prevent script errors from crashing the mod.

## API Reference

For a complete API reference, see the source code in:
- `TestLE/Scripting/LuaAPI.cs` - Main API definitions
- `TestLE/Scripting/LuaManager.cs` - Script management
- `TestLE/Scripting/ScriptManager.cs` - Configuration and lifecycle
