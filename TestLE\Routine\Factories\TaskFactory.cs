using TestLE.Routine.Interfaces;
using TestLE.Routine.Services;
using TestLE.Routine.Tasks;
// ReSharper disable MemberCanBePrivate.Global

namespace TestLE.Routine.Factories;

/// <summary>
/// Factory for creating game tasks with proper dependency injection.
/// Follows Dependency Inversion Principle by abstracting task creation.
/// Follows Open/Closed Principle by allowing new tasks to be added without modifying existing code.
/// </summary>
public class TaskFactory : ITaskFactory
{
    private readonly IStashService _stashService;
    private readonly ILootService _lootService;
    private readonly ICombatService _combatService;
    private readonly INavigationService _navigationService;
    private readonly IGameStateService _gameStateService;

    public TaskFactory(
        IStashService stashService,
        ILootService lootService,
        ICombatService combatService,
        INavigationService navigationService,
        IGameStateService gameStateService)
    {
        _stashService = stashService;
        _lootService = lootService;
        _combatService = combatService;
        _navigationService = navigationService;
        _gameStateService = gameStateService;
    }

    public IEnumerable<IGameTask> CreateTasks()
    {
        // The order in this list defines the bot's decision-making priority.
        // High-priority tasks (like handling death) come first.
        return new List<IGameTask>
        {
            new HandleDeathTask(_gameStateService, _navigationService),
            new CompleteMonolithTask(_stashService, _lootService, _navigationService, _gameStateService),
            new HandleLootTask(_lootService),
            new HandleInteractableTask(_navigationService),
            new HandleGoodShrinesTask(),
            new HandleObjectiveTask(_gameStateService),
            new HandleCombatTask(_combatService, _gameStateService),
            new HandleIdleTask(_navigationService),
            new DefaultWanderTask(_combatService) // Fallback task
        };
    }
}

/// <summary>
/// Simple service locator for dependency injection.
/// In a more complex application, this could be replaced with a proper DI container.
/// </summary>
public static class ServiceProvider
{
    private static IStashService? _stashService;
    private static ILootService? _lootService;
    private static ICombatService? _combatService;
    private static INavigationService? _navigationService;
    private static IGameStateService? _gameStateService;
    private static ITaskFactory? _taskFactory;

    public static IStashService StashService => _stashService ??= new StashService();
    public static ILootService LootService => _lootService ??= new LootService();
    public static ICombatService CombatService => _combatService ??= new CombatService();
    public static INavigationService NavigationService => _navigationService ??= new NavigationService();
    public static IGameStateService GameStateService => _gameStateService ??= new GameStateService();

    public static ITaskFactory TaskFactory => _taskFactory ??= new TaskFactory(
        StashService,
        LootService,
        CombatService,
        NavigationService,
        GameStateService);
}
