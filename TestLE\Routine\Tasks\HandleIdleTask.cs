using System.Collections;
using TestLE.Routine.Interfaces;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Handles the case where the player is idle.
/// Follows SRP by focusing only on idle detection and recovery.
/// </summary>
public class HandleIdleTask : IGameTask
{
    private readonly INavigationService _navigationService;
    private const float IDLE_SECONDS_THRESHOLD = 5f;
    private float _idleTime;

    public HandleIdleTask(INavigationService navigationService)
    {
        _navigationService = navigationService;
    }

    public bool CanExecute()
    {
        // Move to random position if idle for 5 seconds
        if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
        {
            _idleTime += Time.deltaTime;
            if (_idleTime >= IDLE_SECONDS_THRESHOLD)
                return true;
        }
        else
        {
            _idleTime = 0;
        }
        return false;
    }

    public IEnumerator Execute()
    {
        _idleTime = 0;
        var movePos = _navigationService.GetRandomWanderPosition(PLAYER.transform.position, 10f);
        yield return _navigationService.MoveToPosition(movePos);
        yield return new WaitForSeconds(2f);
    }
}
