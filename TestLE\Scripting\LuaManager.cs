using System.Collections;
using System.IO;
using MelonLoader;
using MelonLoader.Utils;
using MoonSharp.Interpreter;

namespace TestLE.Scripting;

/// <summary>
/// Central manager for Lua script execution and lifecycle management.
/// Handles script loading, execution, error handling, and integration with the game routine system.
/// </summary>
public class LuaManager
{
    private static LuaManager? _instance;
    public static LuaManager Instance => _instance ??= new LuaManager();

    private readonly Dictionary<string, Script> _loadedScripts = new();
    private readonly Dictionary<string, ScriptInfo> _scriptInfos = new();
    private readonly string _scriptsDirectory;
    private FileSystemWatcher? _fileWatcher;

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, ScriptInfo> LoadedScripts => _scriptInfos;

    private LuaManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "Scripts");
    }

    /// <summary>
    /// Initialize the Lua manager and set up the scripting environment.
    /// </summary>
    public void Initialize()
    {
        if (IsInitialized)
            return;

        try
        {
            // Ensure scripts directory exists
            Directory.CreateDirectory(_scriptsDirectory);

            // Configure MoonSharp
            UserData.RegisterAssembly();
            Script.DefaultOptions.DebugPrint = s => MelonLogger.Msg($"[Lua] {s}");

            // Set up file watcher for hot reload
            SetupFileWatcher();

            // Load all scripts
            LoadAllScripts();

            IsInitialized = true;
            MelonLogger.Msg($"LuaManager initialized. Scripts directory: {_scriptsDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize LuaManager: {ex.Message}");
            MelonLogger.Error(ex.StackTrace);
        }
    }

    /// <summary>
    /// Shutdown the Lua manager and clean up resources.
    /// </summary>
    public void Shutdown()
    {
        if (!IsInitialized)
            return;

        try
        {
            _fileWatcher?.Dispose();
            _loadedScripts.Clear();
            _scriptInfos.Clear();
            IsInitialized = false;
            MelonLogger.Msg("LuaManager shutdown complete.");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during LuaManager shutdown: {ex.Message}");
        }
    }

    /// <summary>
    /// Load a specific script by name.
    /// </summary>
    public bool LoadScript(string scriptName)
    {
        try
        {
            var scriptPath = Path.Combine(_scriptsDirectory, $"{scriptName}.lua");
            if (!File.Exists(scriptPath))
            {
                MelonLogger.Warning($"Script file not found: {scriptPath}");
                return false;
            }

            var scriptContent = File.ReadAllText(scriptPath);
            var script = new Script();

            // Register the API
            LuaAPI.RegisterAPI(script);

            // Load and compile the script
            script.DoString(scriptContent);

            // Extract script metadata
            var scriptInfo = ExtractScriptInfo(script, scriptName, scriptPath);

            _loadedScripts[scriptName] = script;
            _scriptInfos[scriptName] = scriptInfo;

            MelonLogger.Msg($"Loaded script: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to load script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Unload a specific script.
    /// </summary>
    public bool UnloadScript(string scriptName)
    {
        try
        {
            if (_loadedScripts.Remove(scriptName) && _scriptInfos.Remove(scriptName))
            {
                MelonLogger.Msg($"Unloaded script: {scriptName}");
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to unload script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Execute a function in a loaded script.
    /// </summary>
    public DynValue? ExecuteFunction(string scriptName, string functionName, params object[] args)
    {
        try
        {
            if (!_loadedScripts.TryGetValue(scriptName, out var script))
                return null;

            var function = script.Globals[functionName] as DynValue;
            if (function?.Type != DataType.Function)
                return null;

            return script.Call(function, args);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing function '{functionName}' in script '{scriptName}': {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Check if a script can execute.
    /// </summary>
    public bool CanExecuteScript(string scriptName)
    {
        try
        {
            if (!_scriptInfos.TryGetValue(scriptName, out var scriptInfo) || !scriptInfo.IsEnabled)
                return false;

            var result = ExecuteFunction(scriptName, "CanExecute");
            return result?.Boolean ?? false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Execute a script's main Execute function.
    /// </summary>
    public IEnumerator? ExecuteScript(string scriptName)
    {
        if (!_scriptInfos.TryGetValue(scriptName, out var scriptInfo) || !scriptInfo.IsEnabled)
            return null;

        return ExecuteScriptCoroutine(scriptName);
    }

    private IEnumerator ExecuteScriptCoroutine(string scriptName)
    {
        ExecuteFunction(scriptName, "Execute");
        yield return null;
    }

    /// <summary>
    /// Load all scripts from the scripts directory.
    /// </summary>
    private void LoadAllScripts()
    {
        try
        {
            var luaFiles = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);

            foreach (var filePath in luaFiles)
            {
                var scriptName = Path.GetFileNameWithoutExtension(filePath);
                if (!string.IsNullOrEmpty(scriptName))
                    LoadScript(scriptName);
            }

            MelonLogger.Msg($"Loaded {_loadedScripts.Count} scripts from {_scriptsDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error loading scripts: {ex.Message}");
        }
    }

    /// <summary>
    /// Set up file system watcher for hot reload functionality.
    /// </summary>
    private void SetupFileWatcher()
    {
        try
        {
            _fileWatcher = new FileSystemWatcher(_scriptsDirectory, "*.lua")
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
                EnableRaisingEvents = true
            };

            _fileWatcher.Changed += OnScriptFileChanged;
            _fileWatcher.Created += OnScriptFileChanged;
            _fileWatcher.Deleted += OnScriptFileDeleted;
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Could not set up file watcher: {ex.Message}");
        }
    }

    private void OnScriptFileChanged(object sender, FileSystemEventArgs e)
    {
        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName) && File.Exists(e.FullPath))
        {
            Task.Delay(100).ContinueWith(_ => LoadScript(scriptName));
        }
    }

    private void OnScriptFileDeleted(object sender, FileSystemEventArgs e)
    {
        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName))
            UnloadScript(scriptName);
    }

    /// <summary>
    /// Extract basic script information.
    /// </summary>
    private ScriptInfo ExtractScriptInfo(Script script, string name, string path)
    {
        var info = new ScriptInfo
        {
            Name = name,
            Path = path,
            IsEnabled = true,
            LoadTime = DateTime.Now,
            DisplayName = name,
            Priority = 100
        };

        try
        {
            // Check for required functions
            var canExecute = script.Globals["CanExecute"] as DynValue;
            var execute = script.Globals["Execute"] as DynValue;

            info.HasCanExecute = canExecute?.Type == DataType.Function;
            info.HasExecute = execute?.Type == DataType.Function;

            // Try to get priority
            var priority = script.Globals["SCRIPT_PRIORITY"] as DynValue;
            if (priority?.Type == DataType.Number)
                info.Priority = (int)priority.Number;
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Error extracting script info for '{name}': {ex.Message}");
        }

        return info;
    }
}

/// <summary>
/// Information about a loaded script.
/// </summary>
public class ScriptInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Priority { get; set; } = 100;
    public DateTime LoadTime { get; set; }
    public bool HasCanExecute { get; set; }
    public bool HasExecute { get; set; }
}
