using System.Collections;
using System.IO;
using MelonLoader;
using MelonLoader.Utils;
using MoonSharp.Interpreter;
using TestLE.Routine.Interfaces;

namespace TestLE.Scripting;

/// <summary>
/// Central manager for Lua script execution and lifecycle management.
/// Handles script loading, execution, error handling, and integration with the game routine system.
/// </summary>
public class LuaManager
{
    private static LuaManager? _instance;
    public static LuaManager Instance => _instance ??= new LuaManager();

    private readonly Dictionary<string, Script> _loadedScripts = new();
    private readonly Dictionary<string, ScriptInfo> _scriptInfos = new();
    private readonly string _scriptsDirectory;
    private FileSystemWatcher? _fileWatcher;

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, ScriptInfo> LoadedScripts => _scriptInfos;

    private LuaManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "Scripts");
    }

    /// <summary>
    /// Initialize the Lua manager and set up the scripting environment.
    /// </summary>
    public void Initialize()
    {
        if (IsInitialized)
            return;

        try
        {
            // Ensure scripts directory exists
            Directory.CreateDirectory(_scriptsDirectory);

            // Configure MoonSharp
            UserData.RegisterAssembly();
            Script.DefaultOptions.DebugPrint = s => MelonLogger.Msg($"[Lua] {s}");

            // Set up file watcher for hot reload
            SetupFileWatcher();

            // Load all scripts
            LoadAllScripts();

            IsInitialized = true;
            MelonLogger.Msg($"LuaManager initialized. Scripts directory: {_scriptsDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize LuaManager: {ex.Message}");
            MelonLogger.Error(ex.StackTrace);
        }
    }

    /// <summary>
    /// Shutdown the Lua manager and clean up resources.
    /// </summary>
    public void Shutdown()
    {
        if (!IsInitialized)
            return;

        try
        {
            _fileWatcher?.Dispose();
            _loadedScripts.Clear();
            _scriptInfos.Clear();
            IsInitialized = false;
            MelonLogger.Msg("LuaManager shutdown complete.");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during LuaManager shutdown: {ex.Message}");
        }
    }

    /// <summary>
    /// Load a specific script by name.
    /// </summary>
    public bool LoadScript(string scriptName)
    {
        try
        {
            var scriptPath = Path.Combine(_scriptsDirectory, $"{scriptName}.lua");
            if (!File.Exists(scriptPath))
            {
                MelonLogger.Warning($"Script file not found: {scriptPath}");
                return false;
            }

            var scriptContent = File.ReadAllText(scriptPath);
            var script = new Script();

            // Register the API
            LuaAPI.RegisterAPI(script);

            // Load and compile the script
            script.DoString(scriptContent);

            // Extract script metadata
            var scriptInfo = ExtractScriptInfo(script, scriptName, scriptPath);

            _loadedScripts[scriptName] = script;
            _scriptInfos[scriptName] = scriptInfo;

            MelonLogger.Msg($"Loaded script: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to load script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Unload a specific script.
    /// </summary>
    public bool UnloadScript(string scriptName)
    {
        try
        {
            if (_loadedScripts.Remove(scriptName) && _scriptInfos.Remove(scriptName))
            {
                MelonLogger.Msg($"Unloaded script: {scriptName}");
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to unload script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Execute a function in a loaded script.
    /// </summary>
    public DynValue? ExecuteFunction(string scriptName, string functionName, params object[] args)
    {
        try
        {
            if (!_loadedScripts.TryGetValue(scriptName, out var script))
            {
                MelonLogger.Warning($"Script '{scriptName}' not loaded");
                return null;
            }

            var function = script.Globals[functionName];
            if (function == null || function.Type != DataType.Function)
            {
                MelonLogger.Warning($"Function '{functionName}' not found in script '{scriptName}'");
                return null;
            }

            return script.Call(function, args);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing function '{functionName}' in script '{scriptName}': {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Check if a script can execute (has required functions and conditions are met).
    /// </summary>
    public bool CanExecuteScript(string scriptName)
    {
        try
        {
            if (!_scriptInfos.TryGetValue(scriptName, out var scriptInfo))
                return false;

            if (!scriptInfo.IsEnabled)
                return false;

            // Check if the script has a CanExecute function
            var result = ExecuteFunction(scriptName, "CanExecute");
            return result?.Boolean ?? false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking if script '{scriptName}' can execute: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Execute a script's main Execute function.
    /// </summary>
    public IEnumerator? ExecuteScript(string scriptName)
    {
        try
        {
            if (!_scriptInfos.TryGetValue(scriptName, out var scriptInfo))
                return null;

            if (!scriptInfo.IsEnabled)
                return null;

            var result = ExecuteFunction(scriptName, "Execute");

            // If the script returns a coroutine-like table, convert it
            if (result?.Type == DataType.Table)
            {
                return ConvertLuaCoroutine(result.Table);
            }

            return null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing script '{scriptName}': {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Load all scripts from the scripts directory.
    /// </summary>
    private void LoadAllScripts()
    {
        try
        {
            var luaFiles = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);

            foreach (var filePath in luaFiles)
            {
                var scriptName = Path.GetFileNameWithoutExtension(filePath);
                LoadScript(scriptName);
            }

            MelonLogger.Msg($"Loaded {_loadedScripts.Count} scripts from {_scriptsDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error loading scripts: {ex.Message}");
        }
    }

    /// <summary>
    /// Set up file system watcher for hot reload functionality.
    /// </summary>
    private void SetupFileWatcher()
    {
        try
        {
            _fileWatcher = new FileSystemWatcher(_scriptsDirectory, "*.lua")
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
                EnableRaisingEvents = true
            };

            _fileWatcher.Changed += OnScriptFileChanged;
            _fileWatcher.Created += OnScriptFileChanged;
            _fileWatcher.Deleted += OnScriptFileDeleted;
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Could not set up file watcher: {ex.Message}");
        }
    }

    /// <summary>
    /// Handle script file changes for hot reload.
    /// </summary>
    private void OnScriptFileChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            var scriptName = Path.GetFileNameWithoutExtension(e.Name);

            // Small delay to ensure file write is complete
            Task.Delay(100).ContinueWith(_ =>
            {
                if (File.Exists(e.FullPath))
                {
                    MelonLogger.Msg($"Reloading script: {scriptName}");
                    LoadScript(scriptName);
                }
            });
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error handling script file change: {ex.Message}");
        }
    }

    /// <summary>
    /// Handle script file deletion.
    /// </summary>
    private void OnScriptFileDeleted(object sender, FileSystemEventArgs e)
    {
        try
        {
            var scriptName = Path.GetFileNameWithoutExtension(e.Name);
            UnloadScript(scriptName);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error handling script file deletion: {ex.Message}");
        }
    }

    /// <summary>
    /// Extract metadata and information from a loaded script.
    /// </summary>
    private ScriptInfo ExtractScriptInfo(Script script, string name, string path)
    {
        var info = new ScriptInfo
        {
            Name = name,
            Path = path,
            IsEnabled = true,
            LoadTime = DateTime.Now
        };

        try
        {
            // Try to get script metadata from global variables
            var nameVar = script.Globals["SCRIPT_NAME"];
            if (nameVar != null && nameVar.Type == DataType.String)
                info.DisplayName = nameVar.String;

            var descVar = script.Globals["SCRIPT_DESCRIPTION"];
            if (descVar != null && descVar.Type == DataType.String)
                info.Description = descVar.String;

            var versionVar = script.Globals["SCRIPT_VERSION"];
            if (versionVar != null && versionVar.Type == DataType.String)
                info.Version = versionVar.String;

            var authorVar = script.Globals["SCRIPT_AUTHOR"];
            if (authorVar != null && authorVar.Type == DataType.String)
                info.Author = authorVar.String;

            var priorityVar = script.Globals["SCRIPT_PRIORITY"];
            if (priorityVar != null && priorityVar.Type == DataType.Number)
                info.Priority = (int)priorityVar.Number;

            // Check for required functions
            info.HasCanExecute = script.Globals["CanExecute"]?.Type == DataType.Function;
            info.HasExecute = script.Globals["Execute"]?.Type == DataType.Function;
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Error extracting script info for '{name}': {ex.Message}");
        }

        return info;
    }

    /// <summary>
    /// Convert a Lua table representing a coroutine to a C# IEnumerator.
    /// </summary>
    private IEnumerator? ConvertLuaCoroutine(Table luaTable)
    {
        // This is a simplified implementation
        // In a full implementation, you'd want to handle Lua coroutines properly
        yield return null;
    }
}

/// <summary>
/// Information about a loaded script.
/// </summary>
public class ScriptInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0.0";
    public string Author { get; set; } = "Unknown";
    public string Path { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Priority { get; set; } = 100;
    public DateTime LoadTime { get; set; }
    public bool HasCanExecute { get; set; }
    public bool HasExecute { get; set; }
}
