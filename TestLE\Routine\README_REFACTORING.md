# MainRoutine Refactoring - SOLID Principles Implementation

## Overview
The original `MainRoutine.cs` file was a monolithic 700+ line file that violated several SOLID principles. This refactoring splits it into multiple focused files following SOLID design principles.

## SOLID Principles Applied

### 1. Single Responsibility Principle (SRP)
**Before**: MainRoutine handled orchestration, state management, stashing, combat, loot, navigation, and more.
**After**: Each class has a single, well-defined responsibility:
- `MainRoutineOrchestrator`: Task coordination only
- `StashService`: Stash operations only
- `LootService`: Loot handling only
- `CombatService`: Combat operations only
- `NavigationService`: Movement and navigation only
- `GameStateService`: Game state queries only
- Each task class handles one specific game scenario

### 2. Open/Closed Principle (OCP)
**Before**: Adding new tasks required modifying the MainRoutine class.
**After**: New tasks can be added by implementing `IGameTask` and updating the `TaskFactory` without modifying existing code.

### 3. Liskov Substitution Principle (LSP)
**After**: All task implementations can be substituted for the `IGameTask` interface without breaking functionality.

### 4. Interface Segregation Principle (ISP)
**Before**: Large static helper classes with many unrelated methods.
**After**: Focused interfaces for specific concerns:
- `IStashService`: Stash-specific operations
- `ILootService`: Loot-specific operations
- `ICombatService`: Combat-specific operations
- `INavigationService`: Navigation-specific operations
- `IGameStateService`: Game state queries

### 5. Dependency Inversion Principle (DIP)
**Before**: Direct dependencies on static global variables and concrete classes.
**After**: Dependencies on abstractions through interfaces, with dependency injection via constructor parameters.

## File Structure

```
TestLE/Routine/
├── Interfaces/
│   ├── IGameTask.cs              # Core task interface
│   └── IGameServices.cs          # Service interfaces
├── Services/
│   ├── StashService.cs           # Stash operations
│   ├── LootService.cs            # Loot handling
│   ├── CombatService.cs          # Combat operations
│   ├── NavigationService.cs      # Movement and navigation
│   └── GameStateService.cs       # Game state management
├── Tasks/
│   ├── HandleDeathTask.cs        # Death handling
│   ├── CompleteMonolithTask.cs   # Monolith completion
│   ├── HandleLootTask.cs         # Loot collection
│   ├── HandleInteractableTask.cs # Interactable objects
│   ├── HandleGoodShrinesTask.cs  # Shrine interactions
│   ├── HandleObjectiveTask.cs    # Objective completion
│   ├── HandleCombatTask.cs       # Combat logic
│   ├── HandleIdleTask.cs         # Idle detection
│   └── DefaultWanderTask.cs      # Fallback behavior
├── Factories/
│   └── TaskFactory.cs            # Task creation and DI
├── Configuration/
│   └── GameConfiguration.cs      # Game constants
├── Common/
│   └── SharedRoutines.cs         # Shared functionality
├── MainRoutineOrchestrator.cs    # New orchestrator
└── MainRoutine.cs                # Legacy wrapper (for compatibility)
```

## Benefits

1. **Maintainability**: Each file is focused and easier to understand
2. **Testability**: Services can be easily mocked and tested in isolation
3. **Extensibility**: New tasks and services can be added without modifying existing code
4. **Reusability**: Services can be reused across different tasks
5. **Separation of Concerns**: Clear boundaries between different responsibilities
6. **Dependency Management**: Clear dependency relationships through interfaces

## Backward Compatibility

The original `MainRoutine` class is preserved as a wrapper that delegates to the new `MainRoutineOrchestrator`, ensuring existing code continues to work without modification.

## Configuration

Game constants are centralized in `GameConfiguration.cs` for easy maintenance and modification.

## Dependency Injection

A simple service locator pattern is implemented in `ServiceProvider` for dependency injection. In a more complex application, this could be replaced with a proper DI container.
