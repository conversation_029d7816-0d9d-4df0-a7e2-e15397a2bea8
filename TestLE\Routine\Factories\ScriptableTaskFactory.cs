using TestLE.Routine.Interfaces;
using TestLE.Routine.Services;
using TestLE.Routine.Tasks;
using TestLE.Scripting;

namespace TestLE.Routine.Factories;

/// <summary>
/// Enhanced task factory that includes scriptable tasks alongside regular tasks.
/// Integrates the Lua scripting system with the existing task-based routine architecture.
/// </summary>
public class ScriptableTaskFactory : ITaskFactory
{
    private readonly IStashService _stashService;
    private readonly ILootService _lootService;
    private readonly ICombatService _combatService;
    private readonly INavigationService _navigationService;
    private readonly IGameStateService _gameStateService;
    private readonly bool _enableScriptableTasks;

    public ScriptableTaskFactory(
        IStashService stashService,
        ILootService lootService,
        ICombatService combatService,
        INavigationService navigationService,
        IGameStateService gameStateService,
        bool enableScriptableTasks = true)
    {
        _stashService = stashService;
        _lootService = lootService;
        _combatService = combatService;
        _navigationService = navigationService;
        _gameStateService = gameStateService;
        _enableScriptableTasks = enableScriptableTasks;
    }

    public IEnumerable<IGameTask> CreateTasks()
    {
        var tasks = new List<IGameTask>();

        // Add core system tasks (highest priority)
        tasks.AddRange(CreateCoreTasks());

        // Add scriptable tasks if enabled
        if (_enableScriptableTasks)
        {
            tasks.AddRange(CreateScriptableTasks());
        }

        // Add remaining system tasks
        tasks.AddRange(CreateSystemTasks());

        // Add fallback task (lowest priority)
        tasks.Add(new DefaultWanderTask(_combatService));

        return tasks;
    }

    /// <summary>
    /// Create core system tasks that should have highest priority.
    /// </summary>
    private IEnumerable<IGameTask> CreateCoreTasks()
    {
        return new List<IGameTask>
        {
            new HandleDeathTask(_gameStateService, _navigationService),
            new CompleteMonolithTask(_stashService, _lootService, _navigationService, _gameStateService)
        };
    }

    /// <summary>
    /// Create scriptable tasks from loaded Lua scripts.
    /// </summary>
    private IEnumerable<IGameTask> CreateScriptableTasks()
    {
        try
        {
            // Initialize script manager if not already done
            if (!ScriptManager.Instance.IsInitialized)
            {
                ScriptManager.Instance.Initialize();
            }

            // Get all scriptable tasks
            var scriptableTasks = ScriptableTaskFactory.CreateScriptableTasks().ToList();
            
            // Sort by priority (higher priority first)
            scriptableTasks.Sort((a, b) => b.Priority.CompareTo(a.Priority));

            return scriptableTasks;
        }
        catch (Exception ex)
        {
            MelonLoader.MelonLogger.Error($"Error creating scriptable tasks: {ex.Message}");
            return Enumerable.Empty<IGameTask>();
        }
    }

    /// <summary>
    /// Create remaining system tasks.
    /// </summary>
    private IEnumerable<IGameTask> CreateSystemTasks()
    {
        return new List<IGameTask>
        {
            new HandleLootTask(_lootService),
            new HandleInteractableTask(_navigationService),
            new HandleGoodShrinesTask(),
            new HandleObjectiveTask(_gameStateService),
            new HandleCombatTask(_combatService, _gameStateService),
            new HandleIdleTask(_navigationService)
        };
    }
}

/// <summary>
/// Enhanced service provider that supports scriptable tasks.
/// </summary>
public static class ScriptableServiceProvider
{
    private static IStashService? _stashService;
    private static ILootService? _lootService;
    private static ICombatService? _combatService;
    private static INavigationService? _navigationService;
    private static IGameStateService? _gameStateService;
    private static ITaskFactory? _taskFactory;
    private static ScriptManager? _scriptManager;

    public static IStashService StashService => _stashService ??= new StashService();
    public static ILootService LootService => _lootService ??= new LootService();
    public static ICombatService CombatService => _combatService ??= new CombatService();
    public static INavigationService NavigationService => _navigationService ??= new NavigationService();
    public static IGameStateService GameStateService => _gameStateService ??= new GameStateService();
    public static ScriptManager ScriptManager => _scriptManager ??= ScriptManager.Instance;

    public static ITaskFactory TaskFactory => _taskFactory ??= new ScriptableTaskFactory(
        StashService,
        LootService,
        CombatService,
        NavigationService,
        GameStateService,
        enableScriptableTasks: true);

    /// <summary>
    /// Initialize all services including the scripting system.
    /// </summary>
    public static void Initialize()
    {
        try
        {
            // Initialize script manager
            ScriptManager.Initialize();
            
            MelonLoader.MelonLogger.Msg("ScriptableServiceProvider initialized successfully");
        }
        catch (Exception ex)
        {
            MelonLoader.MelonLogger.Error($"Error initializing ScriptableServiceProvider: {ex.Message}");
        }
    }

    /// <summary>
    /// Shutdown all services including the scripting system.
    /// </summary>
    public static void Shutdown()
    {
        try
        {
            ScriptManager.Shutdown();
            
            // Reset services
            _stashService = null;
            _lootService = null;
            _combatService = null;
            _navigationService = null;
            _gameStateService = null;
            _taskFactory = null;
            _scriptManager = null;
            
            MelonLoader.MelonLogger.Msg("ScriptableServiceProvider shutdown complete");
        }
        catch (Exception ex)
        {
            MelonLoader.MelonLogger.Error($"Error during ScriptableServiceProvider shutdown: {ex.Message}");
        }
    }

    /// <summary>
    /// Reload all scripts and recreate the task factory.
    /// </summary>
    public static void ReloadScripts()
    {
        try
        {
            ScriptManager.ReloadAllScripts();
            
            // Recreate task factory to pick up new scripts
            _taskFactory = new ScriptableTaskFactory(
                StashService,
                LootService,
                CombatService,
                NavigationService,
                GameStateService,
                enableScriptableTasks: true);
            
            MelonLoader.MelonLogger.Msg("Scripts reloaded and task factory updated");
        }
        catch (Exception ex)
        {
            MelonLoader.MelonLogger.Error($"Error reloading scripts: {ex.Message}");
        }
    }
}
