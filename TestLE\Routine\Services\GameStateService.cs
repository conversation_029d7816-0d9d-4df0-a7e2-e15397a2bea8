using TestLE.Routine.Interfaces;
using TestLE.Utilities;

namespace TestLE.Routine.Services;

/// <summary>
/// Single Responsibility: Manages game state queries and operations.
/// Implements IGameStateService interface following Dependency Inversion Principle.
/// </summary>
public class GameStateService : IGameStateService
{
    public bool IsPlayerDead()
    {
        var deathScreen = FindHelpers.FindDeathScreen();
        return deathScreen != null && deathScreen.isActiveAndEnabled;
    }

    public bool IsMonolithComplete()
    {
        var monolithCompleteButton = FindHelpers.FindMonolithCompleteButton();
        if (monolithCompleteButton != null && monolithCompleteButton.gameObject.active)
            return true;
        
        var emphasisEffect = FindHelpers.FindPortalButtonEmphasisEffect();
        return emphasisEffect != null && emphasisEffect.gameObject.active;
    }

    public bool HasObjectives()
    {
        return MONOLITH_OBJECTIVES.Count > 0;
    }

    public void ResetGameState()
    {
        ResetGlobals();
    }
}
