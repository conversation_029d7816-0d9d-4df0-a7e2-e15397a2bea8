﻿using Il2Cpp;
using Il2CppLE.Gameplay.Monolith.Frontend;

namespace TestLE;

public class MonolithObjective
{
    public MonolithPulse? MonolithPulse { get; }


    public MonolithObjective(MonolithPulse monolithPulse)
    {
        MonolithPulse = monolithPulse;

        if (!MONOLITH_OBJECTIVES.Contains(this))
            MONOLITH_OBJECTIVES.Add(this);
    }

    public void RemoveObjective()
    {
        MONOLITH_OBJECTIVES.Remove(this);
    }

    public ActorVisuals? GetEnemyObjective()
    {
        if (MonolithPulse == null || MonolithPulse.completed)
        {
            RemoveObjective();
            return null;
        }

        return MonolithPulse.transform.parent.GetComponentInChildren<ActorVisuals>();
    }

    public WorldObjectClickListener? GetClickObjective()
    {
        if (MonolithPulse == null || MonolithPulse.completed)
        {
            RemoveObjective();
            return null;
        }

        return MonolithPulse.transform.parent.GetComponentInChildren<WorldObjectClickListener>();
    }
}
