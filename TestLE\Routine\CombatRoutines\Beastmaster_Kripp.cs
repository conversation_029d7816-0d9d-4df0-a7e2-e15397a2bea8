﻿using System.Collections;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.CombatRoutines;

public class Beastmaster_Kripp : CombatRoutine
{
    public override float PotionHealthUse => 0f;
    public override bool PickupPotions => false;
    public override int MovementSkillIndex => 2;


    public override IEnumerator OnNewArea()
    {
        if (PlayerHelpers.GetAbilityName(0) is not null and not "Crowstorm")
            PlayerHelpers.UseAbility(0, PLAYER.transform);

        yield break;
    }

    public override IEnumerator Run(Enemy enemy, Transform enemyTransform, float distance)
    {
        if (PlayerHelpers.GetAbilityName(0) is not null and not "Crowstorm")
        {
            PlayerHelpers.UseAbility(0, enemyTransform);
        }
        else if (distance is >= 5 and <= 20 && !PlayerHelpers.IsAbilityOnCooldown(2))
        {
            PlayerHelpers.UseAbility(2, enemyTransform);
        }
        else if (distance <= 5 && !PlayerHelpers.IsAbilityOnCooldown(3))
        {
            PlayerHelpers.UseAbility(3, enemyTransform);
        }
        else if (!PlayerHelpers.IsAbilityOnCooldown(1))
        {
            PlayerHelpers.UseAbility(1, enemyTransform);
        }
        else
        {
            PlayerHelpers.UseAbility(4, enemyTransform);
        }

        yield return new WaitForSeconds(0.1f);
    }
}
