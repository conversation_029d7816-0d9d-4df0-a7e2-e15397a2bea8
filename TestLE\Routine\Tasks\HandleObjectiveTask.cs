using System.Collections;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Handles moving towards and completing monolith objectives.
/// Follows SRP by focusing only on objective completion logic.
/// </summary>
public class HandleObjectiveTask : IGameTask
{
    private readonly IGameStateService _gameStateService;
    private MonolithObjective? _objective;

    public HandleObjectiveTask(IGameStateService gameStateService)
    {
        _gameStateService = gameStateService;
    }

    public bool CanExecute()
    {
        if (!_gameStateService.HasObjectives()) return false;
        _objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        return _objective != null;
    }

    public IEnumerator Execute()
    {
        var enemyObjective = _objective!.GetEnemyObjective();
        if (enemyObjective != null)
        {
            PlayerHelpers.MoveTo(enemyObjective.transform.position);
            yield return new WaitForSeconds(0.3333f);
            yield break;
        }

        var clickObjective = _objective.GetClickObjective();
        if (clickObjective != null)
        {
            var objectiveTransform = clickObjective.transform;
            var objectivePosition = objectiveTransform.position;

            PlayerHelpers.MoveTo(objectivePosition);
            yield return new WaitForSeconds(0.3333f);

            var distance = Vector3.Distance(PLAYER.transform.position, objectiveTransform.position);
            if (distance <= clickObjective.interactionRange)
                clickObjective.ObjectClick(PLAYER.gameObject, true);
            yield break;
        }

        // Objective not valid, remove it
        MONOLITH_OBJECTIVES.Remove(_objective);
    }
}
