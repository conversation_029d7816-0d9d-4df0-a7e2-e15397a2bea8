using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Checks for and handles the player's death.
/// Follows SRP by focusing only on death detection and recovery.
/// </summary>
public class HandleDeathTask : IGameTask
{
    private readonly IGameStateService _gameStateService;
    private readonly INavigationService _navigationService;

    public HandleDeathTask(IGameStateService gameStateService, INavigationService navigationService)
    {
        _gameStateService = gameStateService;
        _navigationService = navigationService;
    }

    public bool CanExecute()
    {
        return _gameStateService.IsPlayerDead();
    }

    public IEnumerator Execute()
    {
        MelonLogger.Msg("Death screen found!");
        var deathScreen = FindHelpers.FindDeathScreen();
        deathScreen!.NormalRespawnClick();
        yield return new WaitForSeconds(1f);
        yield return _navigationService.GoToNextMonolith();
    }
}
