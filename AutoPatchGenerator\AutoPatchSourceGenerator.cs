﻿// AutoPatchSourceGenerator.cs
// <auto-generated>
// Source generator that reads .autopatch files containing lines like:
//   Il2Cpp.MinimapFogOfWar.Initialize -> Postfix
//   Il2Cpp.NonItemPickupableGroundLabel.initialise -> Prefix  (handles generics automatically)
// For each line, it discovers ALL overloads via R<PERSON>lyn, then emits:
//   - AutoPatchEvents with typed events per overload
//   - Harmony patch classes with [HarmonyPatch(typeof(...), "Method", typeof(...), ...)]
// No TargetMethod(), no object[] packing, fully typed.
// </auto-generated>

#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;

namespace AutoPatchGenerator;

[Generator]
public sealed class AutoPatchSourceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // All .autopatch files
        var autopatchTexts =
            context.AdditionalTextsProvider
                .Where(f => f.Path.EndsWith(".autopatch", StringComparison.OrdinalIgnoreCase))
                .Select((file, ct) => file.GetText(ct)?.ToString() ?? string.Empty);

        // We need the Compilation to resolve types + all autopatch lines
        var combined = context.CompilationProvider.Combine(autopatchTexts.Collect());

        context.RegisterSourceOutput(combined, (spc, pair) =>
        {
            var (compilation, files) = pair;

            if (files.IsDefaultOrEmpty)
                return;

            var requests = new List<PatchRequest>();
            foreach (var content in files)
                requests.AddRange(ParsePatches(content));

            if (requests.Count == 0)
                return;

            // Resolve all overloads for each request
            var resolved = ResolveRequests(compilation, requests);

            if (resolved.Count == 0)
                return;

            spc.AddSource("AutoPatchEvents.g.cs", GenerateEvents(resolved));
            spc.AddSource("AutoPatches.g.cs", GenerateHarmonyPatches(resolved));
        });
    }

    // =========================
    // Parsing (.autopatch)
    // =========================
    private static List<PatchRequest> ParsePatches(string content)
    {
        var list = new List<PatchRequest>();
        if (string.IsNullOrWhiteSpace(content))
            return list;

        var lines = content.Split(['\r', '\n'], StringSplitOptions.RemoveEmptyEntries);
        foreach (var raw in lines)
        {
            var line = raw.Trim();
            if (line.Length == 0 || line.StartsWith("#")) continue;

            // Format: Namespace.Type.Method -> PatchType
            // Example: Il2Cpp.MinimapFogOfWar.Initialize -> Postfix
            // Example: Il2Cpp.NonItemPickupableGroundLabel[MonoBehaviour].SetGroundTooltipText -> Postfix
            var parts = line.Split(["->"], StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;

            var left = parts[0].Trim();
            var patchType = parts[1].Trim();

            if (!IsSupportedPatchType(patchType)) continue;

            // Parse the left side, handling generic type specifications
            var (className, methodName, typeArgs) = ParseTypeAndMethod(left);
            if (className.Length == 0 || methodName.Length == 0) continue;

            list.Add(new PatchRequest
            {
                ClassName = className,
                MethodName = methodName,
                PatchType = patchType,
                GenericTypeArgs = typeArgs
            });
        }

        return list;
    }

    private static (string ClassName, string MethodName, List<string> TypeArgs) ParseTypeAndMethod(string input)
    {
        var typeArgs = new List<string>();
        var workingInput = input;

        // Handle generic type specification: Type[GenericArg] or Type[GenericArg1,GenericArg2]
        var bracketStart = workingInput.IndexOf('[');
        if (bracketStart >= 0)
        {
            var bracketEnd = workingInput.LastIndexOf(']');
            if (bracketEnd > bracketStart)
            {
                var genericPart = workingInput.Substring(bracketStart + 1, bracketEnd - bracketStart - 1);
                if (!string.IsNullOrWhiteSpace(genericPart))
                {
                    typeArgs.AddRange(genericPart.Split(',').Select(s => s.Trim()));
                }
                // Remove the generic specification from the input
                workingInput = workingInput.Substring(0, bracketStart) + workingInput.Substring(bracketEnd + 1);
            }
        }

        // Now find the last dot to separate class from method
        var lastDot = workingInput.LastIndexOf('.');
        if (lastDot <= 0) return ("", "", typeArgs);

        var className = workingInput.Substring(0, lastDot).Trim();
        var methodName = workingInput.Substring(lastDot + 1).Trim();

        return (className, methodName, typeArgs);
    }

    private static bool IsSupportedPatchType(string kind)
        => kind is "Prefix" or "Postfix" or "Finalizer";

    // =========================
    // Resolution (Roslyn) - Enhanced for Generics
    // =========================
    private static List<ResolvedOverload> ResolveRequests(Compilation compilation, List<PatchRequest> requests)
    {
        var result = new List<ResolvedOverload>();

        foreach (var req in requests)
        {
            var resolvedTypes = req.GenericTypeArgs.Count > 0 
                ? ResolveTypesWithHint(compilation, req.ClassName, req.GenericTypeArgs)
                : ResolveTypes(compilation, req.ClassName);
            
            foreach (var type in resolvedTypes)
            {
                // All methods with this name (overloads)
                var methods = type.GetMembers()
                    .OfType<IMethodSymbol>()
                    .Where(m =>
                        m.Name == req.MethodName &&
                        (m.MethodKind == MethodKind.Ordinary ||
                         m.MethodKind == MethodKind.PropertyGet ||
                         m.MethodKind == MethodKind.PropertySet))
                    .ToList();

                if (methods.Count == 0)
                    continue;

                // Emit one patch per overload
                for (var i = 0; i < methods.Count; i++)
                {
                    var m = methods[i];

                    // Build a stable, readable suffix for overload
                    var overloadSuffix = methods.Count > 1 ? $"_ovl{i + 1}" : "";
                    
                    // Add type suffix for generic instantiations to avoid conflicts
                    var typeSuffix = "";
                    if (type.IsGenericType && !type.IsUnboundGenericType)
                    {
                        var typeArgs = type.TypeArguments;
                        typeSuffix = "_" + string.Join("_", typeArgs.Select(t => SanitizeIdentifier(t.Name)));
                    }

                    result.Add(new ResolvedOverload
                    {
                        Request = req,
                        OverloadIndex = i + 1,
                        ContainingType = type,
                        Method = m,
                        OverloadSuffix = overloadSuffix + typeSuffix
                    });
                }
            }
        }

        return result;
    }

    private static List<INamedTypeSymbol> ResolveTypes(Compilation compilation, string className)
    {
        var results = new List<INamedTypeSymbol>();
        
        // First try direct resolution (non-generic case)
        var directType = compilation.GetTypeByMetadataName(className);
        if (directType != null)
        {
            results.Add(directType);
            return results;
        }

        // Try to find generic types by searching for patterns like ClassName`1, ClassName`2, etc.
        var genericCandidates = FindGenericTypeDefinitions(compilation, className);
        
        foreach (var genericDef in genericCandidates)
        {
            // Find all constructed versions of this generic type
            var constructedTypes = FindConstructedGenericTypes(compilation, genericDef);
            results.AddRange(constructedTypes);
            
            // If no constructed types found, try to construct using constraint types
            if (constructedTypes.Count == 0)
            {
                var constraintConstructed = ConstructFromConstraints(compilation, genericDef);
                results.AddRange(constraintConstructed);
            }
        }

        return results;
    }

    private static List<INamedTypeSymbol> ResolveTypesWithHint(Compilation compilation, string className, List<string> typeArgs)
    {
        var results = new List<INamedTypeSymbol>();
        
        // If we have type arguments specified, try to resolve them directly
        if (typeArgs.Count > 0)
        {
            var genericDef = compilation.GetTypeByMetadataName($"{className}`{typeArgs.Count}");
            if (genericDef != null)
            {
                var resolvedTypeArgs = new List<ITypeSymbol>();
                foreach (var typeArgName in typeArgs)
                {
                    var typeArg = ResolveTypeArgument(compilation, typeArgName);
                    if (typeArg != null)
                        resolvedTypeArgs.Add(typeArg);
                }
                
                if (resolvedTypeArgs.Count == typeArgs.Count)
                {
                    try
                    {
                        var constructed = genericDef.Construct(resolvedTypeArgs.ToArray());
                        results.Add(constructed);
                        return results;
                    }
                    catch
                    {
                        // Construction failed, fall through to other methods
                    }
                }
            }
        }
        
        // Fall back to normal resolution
        return ResolveTypes(compilation, className);
    }

    private static ITypeSymbol? ResolveTypeArgument(Compilation compilation, string typeName)
    {
        // Try common short names first
        var commonMappings = new Dictionary<string, string>
        {
            {"MonoBehaviour", "UnityEngine.MonoBehaviour"},
            {"GameObject", "UnityEngine.GameObject"},
            {"Transform", "UnityEngine.Transform"},
            {"Component", "UnityEngine.Component"},
            {"Object", "System.Object"},
            {"String", "System.String"},
            {"string", "System.String"},
            {"int", "System.Int32"},
            {"bool", "System.Boolean"},
            {"float", "System.Single"},
            {"double", "System.Double"}
        };

        if (commonMappings.TryGetValue(typeName, out var fullName))
        {
            return compilation.GetTypeByMetadataName(fullName);
        }

        // Try direct resolution
        var direct = compilation.GetTypeByMetadataName(typeName);
        if (direct != null) return direct;

        // Try with common prefixes
        var prefixes = new[] { "System.", "UnityEngine.", "Il2Cpp." };
        foreach (var prefix in prefixes)
        {
            var prefixed = compilation.GetTypeByMetadataName(prefix + typeName);
            if (prefixed != null) return prefixed;
        }

        return null;
    }

    private static List<INamedTypeSymbol> FindGenericTypeDefinitions(Compilation compilation, string className)
    {
        var results = new List<INamedTypeSymbol>();
        
        // Try common generic arities (1-10)
        for (int arity = 1; arity <= 10; arity++)
        {
            var genericName = $"{className}`{arity}";
            var genericType = compilation.GetTypeByMetadataName(genericName);
            if (genericType != null)
            {
                results.Add(genericType);
            }
        }
        
        return results;
    }

    private static List<INamedTypeSymbol> FindConstructedGenericTypes(Compilation compilation, INamedTypeSymbol genericDefinition)
    {
        var results = new List<INamedTypeSymbol>();
        
        // This is the tricky part - we need to find actual instantiations
        // We'll search through all types in all referenced assemblies
        var allTypes = GetAllTypesFromCompilation(compilation);
        
        foreach (var type in allTypes)
        {
            if (type.IsGenericType && 
                !type.IsUnboundGenericType && 
                SymbolEqualityComparer.Default.Equals(type.ConstructedFrom, genericDefinition))
            {
                results.Add(type);
            }
        }
        
        // If no constructed types found, we can try to construct with common types
        if (results.Count == 0)
        {
            results.AddRange(CreateCommonConstructions(compilation, genericDefinition));
        }
        
        return results;
    }

    private static IEnumerable<INamedTypeSymbol> GetAllTypesFromCompilation(Compilation compilation)
    {
        var types = new List<INamedTypeSymbol>();
        
        // Get types from the main assembly
        AddTypesFromNamespace(compilation.Assembly.GlobalNamespace, types);
        
        // Get types from referenced assemblies
        foreach (var reference in compilation.References)
        {
            if (compilation.GetAssemblyOrModuleSymbol(reference) is IAssemblySymbol assembly)
            {
                AddTypesFromNamespace(assembly.GlobalNamespace, types);
            }
        }
        
        return types;
    }

    private static void AddTypesFromNamespace(INamespaceSymbol ns, List<INamedTypeSymbol> types)
    {
        foreach (var member in ns.GetMembers())
        {
            if (member is INamedTypeSymbol type)
            {
                types.Add(type);
                // Add nested types
                AddNestedTypes(type, types);
            }
            else if (member is INamespaceSymbol childNamespace)
            {
                AddTypesFromNamespace(childNamespace, types);
            }
        }
    }

    private static void AddNestedTypes(INamedTypeSymbol type, List<INamedTypeSymbol> types)
    {
        foreach (var member in type.GetMembers())
        {
            if (member is INamedTypeSymbol nestedType)
            {
                types.Add(nestedType);
                AddNestedTypes(nestedType, types);
            }
        }
    }

    private static List<INamedTypeSymbol> CreateCommonConstructions(Compilation compilation, INamedTypeSymbol genericDefinition)
    {
        var results = new List<INamedTypeSymbol>();
        
        // First try to construct using constraint types
        var constraintConstructed = ConstructFromConstraints(compilation, genericDefinition);
        results.AddRange(constraintConstructed);
        
        // If constraint construction didn't work, fall back to common types
        if (results.Count == 0)
        {
            var commonTypes = new[]
            {
                "System.Object",
                "System.String",
                "UnityEngine.MonoBehaviour",
                "UnityEngine.GameObject",
                "UnityEngine.Transform",
                "UnityEngine.Component"
            };

            var typeArgs = new List<ITypeSymbol>();
            foreach (var typeName in commonTypes)
            {
                var type = compilation.GetTypeByMetadataName(typeName);
                if (type != null)
                {
                    typeArgs.Add(type);
                    break; // Just use the first one we find for now
                }
            }

            if (typeArgs.Count > 0 && typeArgs.Count >= genericDefinition.Arity)
            {
                try
                {
                    var constructedType = genericDefinition.Construct(typeArgs.Take(genericDefinition.Arity).ToArray());
                    results.Add(constructedType);
                }
                catch
                {
                    // Construction failed, ignore
                }
            }
        }

        return results;
    }

    private static List<INamedTypeSymbol> ConstructFromConstraints(Compilation compilation, INamedTypeSymbol genericDefinition)
    {
        var results = new List<INamedTypeSymbol>();
        
        if (genericDefinition.TypeParameters.Length == 0)
            return results;

        var constraintTypes = new List<ITypeSymbol>();
        
        foreach (var typeParam in genericDefinition.TypeParameters)
        {
            ITypeSymbol? constraintType = null;
            
            // Look for the first concrete constraint type
            foreach (var constraintTypeSymbol in typeParam.ConstraintTypes)
            {
                if (constraintTypeSymbol.TypeKind == TypeKind.Class || constraintTypeSymbol.TypeKind == TypeKind.Interface)
                {
                    constraintType = constraintTypeSymbol;
                    break;
                }
            }
            
            // If no explicit constraint, use common defaults based on constraint kind
            if (constraintType == null)
            {
                if (typeParam.HasReferenceTypeConstraint)
                {
                    // Try UnityEngine types first for Unity projects
                    constraintType = compilation.GetTypeByMetadataName("UnityEngine.MonoBehaviour") 
                                   ?? compilation.GetTypeByMetadataName("System.Object");
                }
                else if (typeParam.HasValueTypeConstraint)
                {
                    constraintType = compilation.GetTypeByMetadataName("System.Int32");
                }
                else
                {
                    // No specific constraint, default to object
                    constraintType = compilation.GetTypeByMetadataName("System.Object");
                }
            }
            
            if (constraintType != null)
            {
                constraintTypes.Add(constraintType);
            }
            else
            {
                // Can't resolve constraints, return empty
                return results;
            }
        }
        
        if (constraintTypes.Count == genericDefinition.Arity)
        {
            try
            {
                var constructed = genericDefinition.Construct(constraintTypes.ToArray());
                results.Add(constructed);
            }
            catch
            {
                // Construction failed
            }
        }
        
        return results;
    }

    // =========================
    // Codegen - Events (unchanged from original)
    // =========================
    private static SourceText GenerateEvents(List<ResolvedOverload> overloads)
    {
        var sb = new StringBuilder();
        sb.AppendLine("// <auto-generated/>");
        sb.AppendLine("using System;");
        sb.AppendLine();

        // Group overloads by class
        var grouped = overloads.GroupBy(ro => ro.ContainingType, SymbolEqualityComparer.Default);

        foreach (var group in grouped)
        {
            var type = (INamedTypeSymbol)group.Key; // Cast to INamedTypeSymbol
            var className = SanitizeIdentifier($"Patches_{GetTypeDisplayName(type)}");

            sb.AppendLine($"public static class {className}");
            sb.AppendLine("{");

            // Generate all delegate types for this class INSIDE the class
            foreach (var ro in group)
            {
                var req = ro.Request;
                var m = ro.Method;
                var eventName = $"On{req.MethodName}{req.PatchType}{ro.OverloadSuffix}";
                var delegateName = $"{eventName}Delegate";

                // Build delegate parameter list with names
                var delegateParams = new List<(string Type, string Name)>();
                if (!m.IsStatic)
                    delegateParams.Add((DisplayType(ro.ContainingType, fullyQualified: true), "__instance"));

                foreach (var p in m.Parameters)
                    delegateParams.Add((DisplayType(UnderlyingTypeForEvent(p), fullyQualified: true), p.Name));

                if (req.PatchType == "Postfix" && !m.ReturnsVoid)
                    delegateParams.Add(("ref " + DisplayType(m.ReturnType, fullyQualified: true), "__result"));

                if (req.PatchType == "Finalizer")
                    delegateParams.Add(("System.Exception", "__exception"));

                sb.Append("    public delegate void ").Append(delegateName).Append('(');
                sb.Append(string.Join(", ", delegateParams.Select(p => $"{p.Type} {p.Name}")));
                sb.AppendLine(");");

                // Generate event using custom delegate
                sb.AppendLine($"    public static event {delegateName} {eventName};");

                // Fire method (same parameter types, with names)
                var fireParams = new List<(string Type, string Name)>();
                if (!m.IsStatic)
                    fireParams.Add((DisplayType(ro.ContainingType, fullyQualified: true), "__instance"));

                foreach (var p in m.Parameters)
                    fireParams.Add((DisplayType(UnderlyingTypeForEvent(p), fullyQualified: true), p.Name));

                if (req.PatchType == "Postfix" && !m.ReturnsVoid)
                    fireParams.Add(("ref " + DisplayType(m.ReturnType, fullyQualified: true), "__result"));

                if (req.PatchType == "Finalizer")
                    fireParams.Add(("System.Exception", "__exception"));

                sb.Append("    internal static void Fire").Append(eventName).Append('(');
                sb.Append(string.Join(", ", fireParams.Select(t => $"{t.Type} {t.Name}")));
                sb.AppendLine(")");
                sb.Append("        => ").Append(eventName).Append("?.Invoke(");
                // For Postfix with ref __result, pass 'ref __result' instead of just '__result'
                var fireArgs = new List<string>();
                foreach (var fp in fireParams)
                {
                    if (req.PatchType == "Postfix" && !m.ReturnsVoid && fp.Name == "__result")
                        fireArgs.Add($"ref {fp.Name}");
                    else
                        fireArgs.Add(fp.Name);
                }
                sb.Append(string.Join(", ", fireArgs));
                sb.AppendLine(");");
                sb.AppendLine();
            }

            sb.AppendLine("}");
            sb.AppendLine();
        }
        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }

    // =========================
    // Codegen - Harmony Patches (unchanged from original)
    // =========================
    private static SourceText GenerateHarmonyPatches(List<ResolvedOverload> overloads)
    {
        var sb = new StringBuilder();
        sb.AppendLine("// <auto-generated/>");
        sb.AppendLine("using System;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine();

        foreach (var ro in overloads)
        {
            var req = ro.Request;
            var m = ro.Method;
            var typeFqn = DisplayType(ro.ContainingType, fullyQualified: true);

            // Class name for patch
            var patchClassName = SanitizeIdentifier($"{GetTypeDisplayName(ro.ContainingType)}_{m.Name}_{req.PatchType}{ro.OverloadSuffix}_Patch");
            var eventClassName = SanitizeIdentifier($"Patches_{GetTypeDisplayName(ro.ContainingType)}");

            // Build the HarmonyPatch attributes
            sb.AppendLine($"[HarmonyPatch(typeof({typeFqn}))]");

            // typeof(...) list for parameters
            var typeofParams = string.Join(", ", m.Parameters.Select(p => $"typeof({DisplayType(p.Type, fullyQualified: true)})"));
            if (typeofParams.Length > 0)
                sb.AppendLine($"[HarmonyPatch(\"{m.Name}\", {typeofParams})]");
            else
                sb.AppendLine($"[HarmonyPatch(\"{m.Name}\")]");

            sb.AppendLine($"public static class {patchClassName}");
            sb.AppendLine("{");

            // Build method parameter list for the patch method (with ref/out/in modifiers)
            var patchParams = new List<string>();
            if (!m.IsStatic)
                patchParams.Add($"{typeFqn} __instance");

            foreach (var p in m.Parameters)
            {
                var mod = ParamModifier(p.RefKind); // ref/out/in/none
                var pType = DisplayType(UnderlyingTypeForPatchSignature(p), fullyQualified: true);
                patchParams.Add($"{mod}{pType} {p.Name}".Trim());
            }

            switch (req.PatchType)
            {
                case "Prefix":
                {
                    sb.AppendLine("    [HarmonyPrefix]");
                    sb.AppendLine($"    private static void Prefix({string.Join(", ", patchParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"{eventClassName}.FireOn{req.MethodName}Prefix{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("    }");
                    break;
                }

                case "Postfix":
                {
                    // Include __result if non-void
                    var localParams = new List<string>(patchParams);
                    if (!m.ReturnsVoid)
                        localParams.Add($"ref {DisplayType(m.ReturnType, fullyQualified: true)} __result");

                    sb.AppendLine("    [HarmonyPostfix]");
                    sb.AppendLine($"    private static void Postfix({string.Join(", ", localParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"{eventClassName}.FireOn{req.MethodName}Postfix{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    if (!m.ReturnsVoid) args.Add("ref __result");
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("    }");
                    break;
                }

                case "Finalizer":
                {
                    var localParams = new List<string>(patchParams)
                    {
                        "System.Exception __exception"
                    };

                    sb.AppendLine("    [HarmonyFinalizer]");
                    sb.AppendLine($"    private static System.Exception Finalizer({string.Join(", ", localParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"{eventClassName}.FireOn{req.MethodName}Finalizer{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    args.Add("__exception");
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("        return __exception;"); // pass-through behavior
                    sb.AppendLine("    }");
                    break;
                }
            }

            sb.AppendLine("}");
            sb.AppendLine();
        }

        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }

    // =========================
    // Helpers
    // =========================

    private static string GetTypeDisplayName(INamedTypeSymbol type)
    {
        if (type.IsGenericType && !type.IsUnboundGenericType)
        {
            var baseName = type.ConstructedFrom.Name;
            var typeArgs = string.Join("_", type.TypeArguments.Select(t => t.Name));
            return $"{baseName}_of_{typeArgs}";
        }
        return type.Name;
    }

    // For events we want by-value types (no ref/out/in), so use the element type if by-ref.
    private static ITypeSymbol UnderlyingTypeForEvent(IParameterSymbol p)
    {
        // Roslyn models ref/out as RefKind + the same Type; Action<T> can't use ref/out anyway.
        // We keep the declared type (which is the element type) for event delegates.
        return p.Type;
    }

    // For patch method signatures we keep the ref/out/in modifier, but the *type* is still the declared type.
    // Roslyn already exposes the element as Type; the ref-ness is in RefKind.
    private static ITypeSymbol UnderlyingTypeForPatchSignature(IParameterSymbol p) => p.Type;

    private static string ParamModifier(RefKind rk) => rk switch
    {
        RefKind.None => string.Empty,
        RefKind.Ref  => "ref ",
        RefKind.Out  => "out ",
        RefKind.In   => "in ",
        _ => string.Empty
    };

    private static string DisplayType(ITypeSymbol t, bool fullyQualified)
    {
        var fmt = fullyQualified
            ? SymbolDisplayFormat.FullyQualifiedFormat
            : SymbolDisplayFormat.MinimallyQualifiedFormat;

        // Important: FullyQualifiedFormat yields "global::Ns.Type" which is valid in typeof(...) and signatures.
        return t.ToDisplayString(fmt);
    }

    private static string SanitizeIdentifier(string raw)
    {
        if (string.IsNullOrEmpty(raw)) return "_GeneratedPatch";
        var sb = new StringBuilder(raw.Length);
        foreach (var ch in raw)
            sb.Append(char.IsLetterOrDigit(ch) || ch == '_' ? ch : '_');
        if (sb.Length > 0 && char.IsDigit(sb[0])) sb.Insert(0, '_');
        return sb.ToString();
    }

    // =========================
    // Models
    // =========================
    private sealed class PatchRequest
    {
        public string ClassName { get; set; } = "";
        public string MethodName { get; set; } = "";
        public string PatchType { get; set; } = ""; // Prefix|Postfix|Finalizer
        public List<string> GenericTypeArgs { get; set; } = new();
    }

    private sealed class ResolvedOverload
    {
        public PatchRequest Request { get; set; } = default!;
        public INamedTypeSymbol ContainingType { get; set; } = default!;
        public IMethodSymbol Method { get; set; } = default!;
        public int OverloadIndex { get; set; }
        public string OverloadSuffix { get; set; } = ""; // e.g., "_ovl1" or "_ovl1_MonoBehaviour"
    }
}