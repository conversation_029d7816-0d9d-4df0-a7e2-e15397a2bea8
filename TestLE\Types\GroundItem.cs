﻿using System.Collections;
using Il2Cpp;
using Il2CppLE.Data;
using TestLE.Utilities;
// ReSharper disable MemberCanBePrivate.Global

namespace TestLE.Types;

public class GroundItem
{
    public PickupableGroundLabel Label { get; }

    public GroundItem(PickupableGroundLabel label)
    {
        Label = label;

        if (GROUND_ITEMS.Any(i => i?.Label == label))
            return;

        GROUND_ITEMS.Add(this);
        LAST_GROUND_ITEM_DROP = DateTime.Now;
    }

    public void Pickup()
    {
        if (!IsValid()) return;

        switch (Label)
        {
            case GroundItemLabel item:
                item.ClickedItem();
                break;
            case GroundGoldLabel gold:
                gold.Clicked();
                break;
            case GroundPotionLabel potion:
                potion.Clicked();
                break;
            case AncientBoneLabel bone:
                bone.Clicked();
                break;
        }

        GROUND_ITEMS.Remove(this);
    }

    public IEnumerator MoveToItem()
    {
        if (!IsValid())
        {
            GROUND_ITEMS.Remove(this);
            yield break;
        }

        var position = Label switch
        {
            GroundItemLabel item => item.visuals.transform.position,
            GroundGoldLabel gold => gold.visuals.transform.position,
            GroundPotionLabel potion => potion.visuals.transform.position,
            AncientBoneLabel bone => bone.visuals.transform.position,
            _ => Label.transform.position
        };

        yield return PlayerHelpers.MoveToForce(position);
    }

    private bool IsValid() => Label.gameObject.active;
}
