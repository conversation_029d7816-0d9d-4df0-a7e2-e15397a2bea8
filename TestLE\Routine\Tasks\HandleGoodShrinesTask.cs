using System.Collections;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Handles good shrines before map objectives.
/// Follows SRP by focusing only on shrine interaction logic.
/// </summary>
public class HandleGoodShrinesTask : IGameTask
{
    public bool CanExecute()
    {
        return GOOD_SHRINES.Count > 0;
    }

    public IEnumerator Execute()
    {
        // Clean up null shrines
        for (var i = 0; i < GOOD_SHRINES.Count; i++)
        {
            var s = GOOD_SHRINES[i];
            if (s != null)
                continue;
            
            GOOD_SHRINES.RemoveAt(i);
            i--;
        }

        // Sort shrines by distance
        var playerPos = PLAYER.transform.position;
        GOOD_SHRINES.Sort((v1, v2) => Vector3.Distance(playerPos, v1!.transform.position).CompareTo(Vector3.Distance(playerPos, v2!.transform.position)));

        var shrine = GOOD_SHRINES.First();
        PlayerHelpers.MoveTo(shrine!.transform.position);
        yield return new WaitForSeconds(0.3333f);

        // Check if we're in range and interact
        var distance = Vector3.Distance(PLAYER.transform.position, shrine.transform.position);
        if (distance <= shrine.interactionRange)
        {
            shrine.ObjectClick(PLAYER.gameObject, true);
            GOOD_SHRINES.Remove(shrine);
        }
    }
}
