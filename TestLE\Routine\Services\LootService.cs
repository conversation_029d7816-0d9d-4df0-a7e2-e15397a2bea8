using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;

namespace TestLE.Routine.Services;

/// <summary>
/// Single Responsibility: Manages all loot-related operations.
/// Implements ILootService interface following Dependency Inversion Principle.
/// </summary>
public class LootService : ILootService
{
    public IEnumerator HandleLoot()
    {
        MelonLogger.Msg("Handling loot!");
        var groundItem = GROUND_ITEMS.FirstOrDefault();
        if (groundItem == null)
        {
            MelonLogger.Msg("Ground item is null!");
            if (GROUND_ITEMS.Count > 0)
                GROUND_ITEMS.RemoveAt(0);
            yield break;
        }

        MelonLogger.Msg("Moving to ground item!");
        yield return groundItem.MoveToItem();
        groundItem.Pickup();
    }

    public bool HasLootAvailable()
    {
        // Check if we are in range of loot, if so, loot
        var enemyResult = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        return enemyResult.distance > 3f && GROUND_ITEMS.Count > 0;
    }
}
