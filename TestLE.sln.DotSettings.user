﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005C0Harmony_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMelonLoader_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CDependencies_005CSupportModules_005CIl2CppInterop_002ECommon_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CDependencies_005CSupportModules_005CIl2CppInterop_002EHarmonySupport_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CDependencies_005CSupportModules_005CIl2CppInterop_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppAwesomeTechnologies_002ETouchReactSystemPro_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppAwesomeTechnologies_002EVegetationStudioPro_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCinemachine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCommandLine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppcrosstales_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCSharp_002EOperationResult_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDialogueSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDOTween_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDOTweenPro_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDunGen_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppEHRGB_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFacepunch_002ESteamworks_002EWin64_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFMODUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFMODUnityResonance_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppGenerated_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppHeathen_002EScreenKeyboard_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppHoudiniEngineUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppJBooth_002EMicroSplat_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ENetworking_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ETelemetry_002EClient_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ETelemetry_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002EUI_002EControls_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLidgren_002ENetwork_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLuaInterpreter_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMathNet_002ENumerics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMeshExtension_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMono_002ESecurity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppmscorlib_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppnetstandard_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewAssembly_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewtonsoft_002EJson_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewtonsoft_002EJson_002EUnityConverters_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppPlayFab_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppPolly_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppProBuilderCore_002DUnity5_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppProBuilderMeshOps_002DUnity5_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRewired_005FCore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRewired_005FWindows_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRG_002EImGui_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRG_002EImGui_002EUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSendBird_002EUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EOdinInspector_002EAttributes_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EOdinInspector_002ECompatibilityLayer_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002EAOTGenerated_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002EConfig_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EUtilities_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EConfiguration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EData_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EDiagnostics_002EStackTrace_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EGlobalization_002EExtensions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EIO_002ECompression_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EIO_002ECompression_002EFileSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ENet_002EHttp_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ENumerics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ERuntime_002ECompilerServices_002EUnsafe_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ERuntime_002ESerialization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EValueTuple_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EXml_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EXml_002ELinq_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppTriangleNET_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FContent_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FCore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FExamples_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUniTask_002EAddressables_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUniTask_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppwebsocket_002Dsharp_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppXNode_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CMethodAddressToToken_002Edb/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CMethodXrefScanCache_002Edb/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ECoreModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EAddressables_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EBurst_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EBurst_002EUnsafe_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ECollections_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EJobs_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ELocalization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EMathematics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ENetworking_002ETransport_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EPostprocessing_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ERenderPipelines_002ECore_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EResourceManager_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002EAnalytics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EConfiguration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EDevice_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EEnvironments_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EEnvironments_002EInternal_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EInternal_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002ERegistration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EScheduler_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002ETelemetry_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EThreading_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ETextMeshPro_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ETimeline_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAccessibilityModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAndroidJNIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAnimationModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EARModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAssetBundleModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAudioModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EClothModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EClusterInputModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EClusterRendererModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ECrashReportingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EDirectorModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EDSPGraphModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGameCenterModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGridModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EHotReloadModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EImageConversionModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EIMGUIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EInputLegacyModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EInputModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EJSONSerializeModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ELocalizationModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EParticleSystemModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPerformanceReportingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPhysics2DModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPhysicsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EProfilerModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EScreenCaptureModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESharedInternalsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESpriteMaskModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESpriteShapeModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EStreamingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESubstanceModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESubsystemsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETerrainModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETerrainPhysicsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETextCoreModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETextRenderingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETilemapModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETLSModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUI_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUIElementsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUmbraModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUNETModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityAnalyticsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityConnectModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityTestProtocolModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestAssetBundleModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestAudioModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestTextureModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestWWWModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVehiclesModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVFXModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVideoModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVRModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EWindModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EXRModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_005CMelonLoader_005Cnet6_005CMelonLoader_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppALINE_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppAwesomeTechnologies_002ETouchReactSystemPro_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppAwesomeTechnologies_002EVegetationStudioPro_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppBezierSolution_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCinemachine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCommandLine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCrest_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppcrosstales_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppCSharp_002EOperationResult_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDialogueSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDOTween_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDOTweenPro_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppDunGen_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppEHRGB_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFacepunch_002ESteamworks_002EWin64_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFmodShared_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFMODUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppFMODUnityResonance_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppGenerated_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppHeathen_002EScreenKeyboard_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppHoudiniEngineUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppJBooth_002EMicroSplat_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ENetworking_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002EPCG_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ETelemetry_002EClient_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002ETelemetry_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLE_002EUI_002EControls_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLidgren_002ENetwork_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppLuaInterpreter_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMathNet_002ENumerics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMeshExtension_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppMono_002ESecurity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cppmscorlib_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewAssembly_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewtonsoft_002EJson_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppNewtonsoft_002EJson_002EUnityConverters_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppPlayFab_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppPolly_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppProBuilderCore_002DUnity5_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppProBuilderMeshOps_002DUnity5_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRewired_005FCore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRewired_005FWindows_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRewired_005FWindows_005FFunctions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRG_002EImGui_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppRG_002EImGui_002EUnity_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EOdinInspector_002EAttributes_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EOdinInspector_002EModules_002EUnity_002EAddressables_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EOdinInspector_002EModules_002EUnityLocalization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002EAOTGenerated_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002EConfig_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002ESerialization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSirenix_002EUtilities_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppStreamChat_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppStreamChat_002ELibs_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EComponentModel_002EDataAnnotations_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EConfiguration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EData_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EDrawing_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EIO_002ECompression_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EIO_002ECompression_002EFileSystem_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ENet_002EHttp_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ENumerics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ERuntime_002ECompilerServices_002EUnsafe_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002ERuntime_002ESerialization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EXml_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppSystem_002EXml_002ELinq_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppTriangleNET_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FContent_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FCore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUMA_005FExamples_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUniTask_002EAddressables_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppUniTask_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2CppXNode_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CIl2Cpp_005F_005FGenerated_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EAddressables_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EAI_002ENavigation_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EBurst_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EBurst_002EUnsafe_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ECollections_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ECollections_002ELowLevel_002EILSupport_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ELocalization_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EMathematics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EMemoryProfiler_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EPostprocessing_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EProfiling_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ERenderPipelines_002ECore_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EResourceManager_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002EAnalytics_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EConfiguration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EDevice_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EEnvironments_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EEnvironments_002EInternal_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EInternal_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002ERegistration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EScheduler_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002ETelemetry_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002EServices_002ECore_002EThreading_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ETextMeshPro_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnity_002ETimeline_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAccessibilityModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAndroidJNIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAnimationModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EARModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAssetBundleModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EAudioModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EClothModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EContentLoadModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ECoreModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ECrashReportingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EDirectorModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EDSPGraphModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGameCenterModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGraphicsStateCollectionSerializerModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EGridModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EHierarchyCoreModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EHotReloadModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EImageConversionModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EIMGUIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EInputForUIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EInputLegacyModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EInputModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EJSONSerializeModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ELocalizationModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EMarshallingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EMultiplayerModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EParticleSystemModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPerformanceReportingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPhysics2DModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPhysicsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EPropertiesModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ERuntimeInitializeOnLoadManagerInitializerModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EScreenCaptureModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EShaderVariantAnalyticsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESharedInternalsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESpriteMaskModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESpriteShapeModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EStreamingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESubstanceModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ESubsystemsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETerrainModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETerrainPhysicsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETextCoreFontEngineModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETextCoreTextEngineModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETextRenderingModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETilemapModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002ETLSModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUI_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUIElementsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUIModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUmbraModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityAnalyticsCommonModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityAnalyticsModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityConnectModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityCurlModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityTestProtocolModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestAssetBundleModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestAudioModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestTextureModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EUnityWebRequestWWWModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVehiclesModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVFXModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVideoModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EVRModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EWindModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005CIl2CppAssemblies_005CUnityEngine_002EXRModule_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005C0Harmony_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAsmResolver_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAsmResolver_002EDotNet_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAsmResolver_002EPE_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAsmResolver_002EPE_002EFile_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAssetRipper_002EPrimitives_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CAssetsTools_002ENET_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CbHapticsLib_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIced_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIl2CppInterop_002ECommon_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIl2CppInterop_002EGenerator_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIl2CppInterop_002EHarmonySupport_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIl2CppInterop_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CIndexRange_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMelonLoader_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMelonLoader_002ENativeHost_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EBcl_002EAsyncInterfaces_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EDiagnostics_002ENETCore_002EClient_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EDiagnostics_002ERuntime_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EConfiguration_002EAbstractions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EConfiguration_002EBinder_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EConfiguration_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EDependencyInjection_002EAbstractions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002ELogging_002EAbstractions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002ELogging_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EOptions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMicrosoft_002EExtensions_002EPrimitives_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMono_002ECecil_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMono_002ECecil_002EMdb_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMono_002ECecil_002EPdb_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMono_002ECecil_002ERocks_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMonoMod_002EBackports_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMonoMod_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMonoMod_002EILHelpers_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMonoMod_002ERuntimeDetour_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CMonoMod_002EUtils_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CNewtonsoft_002EJson_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CSystem_002EConfiguration_002EConfigurationManager_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CSystem_002ESecurity_002ECryptography_002EProtectedData_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CSystem_002ESecurity_002EPermissions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CSystem_002EWindows_002EExtensions_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CTomlet_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CUnityEngine_002EIl2CppAssetBundleManager_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CUnityEngine_002EIl2CppImageConversionManager_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=H_003A_005CSteamLibrary_005Csteamapps_005Ccommon_005CLast_0020Epoch_0020_002D_0020Copy_005CMelonLoader_005Cnet6_005CWebSocketDotNet_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AAutoPatches_002Eg_002Ecs_002Fl_003AC_0021_003FUsers_003FBJS_003FAppData_003FLocal_003FTemp_003FSourceGeneratedDocuments_003F8B67515CBF6AC8EDCC430B94_003FAutoPatchGenerator_003FAutoPatchSourceGenerator_003FAutoPatches_002Eg_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/BRACES_FOR_FOR/@EntryValue">RequiredForMultiline</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/BRACES_FOR_IFELSE/@EntryValue">NotRequired</s:String>
	<s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/BRACES_REDUNDANT/@EntryValue">False</s:Boolean>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/MODIFIERS_ORDER/@EntryValue">public override protected internal file new private async virtual sealed abstract static extern unsafe volatile readonly required</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/PARENTHESES_REDUNDANCY_STYLE/@EntryValue">Remove</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpCodeStyle/ThisQualifier/INSTANCE_MEMBERS_QUALIFY_DECLARED_IN/@EntryValue">0</s:String>
	<s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/BLANK_LINES_AFTER_START_COMMENT/@EntryValue">2</s:Int64>
	<s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/BLANK_LINES_AROUND_AUTO_PROPERTY/@EntryValue">0</s:Int64>
	<s:Boolean x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/LINE_FEED_AT_FILE_END/@EntryValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_EXPR_METHOD_ON_SINGLE_LINE/@EntryValue">ALWAYS</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_EXPR_PROPERTY_ON_SINGLE_LINE/@EntryValue">ALWAYS</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/PLACE_SIMPLE_EMBEDDED_STATEMENT_ON_SAME_LINE/@EntryValue">NEVER</s:String>
	<s:String x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/WRAP_ARRAY_INITIALIZER_STYLE/@EntryValue">CHOP_IF_LONG</s:String>
	<s:Int64 x:Key="/Default/CodeStyle/CodeFormatting/CSharpFormat/WRAP_LIMIT/@EntryValue">202</s:Int64>
	<s:String x:Key="/Default/Environment/AssemblyExplorer/XmlDocument/@EntryValue">&lt;AssemblyExplorer&gt;&#xD;
  &lt;Assembly Path="D:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.dll" /&gt;&#xD;
&lt;/AssemblyExplorer&gt;</s:String>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpKeepExistingMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpPlaceEmbeddedOnSameLineMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ECSharpUseContinuousIndentInsideBracesMigration/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/SettingsMigration/IsMigratorApplied/=JetBrains_002EReSharper_002EPsi_002ECSharp_002ECodeStyle_002ESettingsUpgrade_002EMigrateBlankLinesAroundFieldToBlankLinesAroundProperty/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>