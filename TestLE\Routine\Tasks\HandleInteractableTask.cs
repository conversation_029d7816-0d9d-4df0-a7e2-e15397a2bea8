using System.Collections;
using Il2Cpp;
using TestLE.Routine.Interfaces;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Finds and interacts with world objects like interactables.
/// Follows SRP by focusing only on interactable object handling.
/// </summary>
public class HandleInteractableTask : IGameTask
{
    private readonly INavigationService _navigationService;
    private WorldObjectClickListener? _targetInteractable;

    public HandleInteractableTask(INavigationService navigationService)
    {
        _navigationService = navigationService;
    }

    public bool CanExecute()
    {
        // Move to interactable and click it if it is within range
        for (var i = 0; i < INTERACTABLES.Count; i++)
        {
            var interactable = INTERACTABLES[i];
            if (interactable == null || !interactable.isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
                i--;
                continue;
            }

            var interactablePos = interactable.transform.position;
            if (Vector3.Distance(PLAYER.transform.position, interactablePos) > 20f)
                continue;

            _targetInteractable = interactable;
            return true;
        }
        return false;
    }

    public IEnumerator Execute()
    {
        yield return _navigationService.MoveToPosition(_targetInteractable!.transform.position, _targetInteractable.interactionRange * 0.8f);
        INTERACTABLES.Remove(_targetInteractable);
        _targetInteractable.ObjectClick(PLAYER.gameObject, true);
    }
}
