using System.Collections;
using TestLE.Routine.Common;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TestLE.Routine.Services;

/// <summary>
/// Single Responsibility: Manages all navigation and movement operations.
/// Implements INavigationService interface following Dependency Inversion Principle.
/// </summary>
public class NavigationService : INavigationService
{
    public IEnumerator MoveToPosition(Vector3 position, float stoppingDistance = 1f)
    {
        yield return PlayerHelpers.MoveToForce(position, stoppingDistance);
    }

    public IEnumerator GoToNextMonolith()
    {
        yield return SharedRoutines.GoNextMonolith();
    }

    public Vector3 GetRandomWanderPosition(Vector3 currentPosition, float range)
    {
        if (UnityHelpers.RandomPointOnNavMesh(currentPosition, range, out var movePos))
            return movePos;

        return currentPosition + new Vector3(Random.Range(-range, range), 0, Random.Range(-range, range));
    }
}
