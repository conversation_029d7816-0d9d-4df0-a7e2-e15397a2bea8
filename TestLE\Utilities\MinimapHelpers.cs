﻿using Il2Cpp;
using Il2CppDMM;
using Il2CppLE.Interactions.KeyProviders;
using UnityEngine;

namespace TestLE.Utilities;

public static class MinimapHelpers
{
    public static DMMapIcon CreateIconForActorDisplayInformation(ActorDisplayInformation displayInfo)
    {
        var iconObject = new GameObject($"Minimap icon for {displayInfo.gameObject.name}");
        iconObject.SetActive(false);

        var mapIcon = iconObject.AddComponent<DMMapIcon>();
        mapIcon.icon = Assets.DotSprite;
        mapIcon.scaleMultiplier = displayInfo.actorClass switch
        {
            DisplayActorClass.Normal => 0.15f,
            DisplayActorClass.Magic => 0.2f,
            DisplayActorClass.Rare => 0.3f,
            DisplayActorClass.Boss => 0.5f,
            _ => 1f
        };
        mapIcon.tint = displayInfo.actorClass switch
        {
            DisplayActorClass.Normal => new Color32(244, 67, 54, 255),
            DisplayActorClass.Magic => new Color32(41, 182, 246, 255),
            DisplayActorClass.Rare => new Color32(255, 255, 0, 255),
            DisplayActorClass.Boss => new Color32(191, 54, 12, 255),
            _ => Color.white
        };

        var followTarget = iconObject.AddComponent<FollowTarget>();
        followTarget.Target = displayInfo.transform;

        iconObject.SetActive(true);

        return mapIcon;
    }

    public static DMMapIcon CreateIconForDungeonExit(DungeonExitKeyProvider dungeonExitKeyProvider)
    {
        var iconObject = new GameObject($"Minimap icon for {dungeonExitKeyProvider.gameObject.name}");
        iconObject.SetActive(false);

        var mapIcon = iconObject.AddComponent<DMMapIcon>();
        mapIcon.icon = Assets.DotSprite;
        mapIcon.scaleMultiplier = 1f;
        mapIcon.tint = Color.white;

        var followTarget = iconObject.AddComponent<FollowTarget>();
        followTarget.Target = dungeonExitKeyProvider.transform;

        iconObject.SetActive(true);

        return mapIcon;
    }

    public static GameObject? GetShrineMinimapIcon(this ShrineVisualsCreator shrine)
    {
        var minimapIcon = shrine.GetComponentInChildren<WorldAreaEnterListener>().transform.GetChild(0);
        return minimapIcon != null ? minimapIcon.gameObject : null;
    }
}
