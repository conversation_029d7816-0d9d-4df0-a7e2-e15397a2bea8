namespace TestLE.Routine.Configuration;

/// <summary>
/// Configuration class for game settings and constants.
/// Follows Single Responsibility Principle by managing only configuration data.
/// </summary>
public static class GameConfiguration
{
    /// <summary>
    /// Time threshold for considering the player idle (in seconds).
    /// </summary>
    public const float IdleTimeThreshold = 5f;

    /// <summary>
    /// Maximum distance to search for enemies.
    /// </summary>
    public const float MaxEnemySearchDistance = 100f;

    /// <summary>
    /// Distance threshold for loot safety (enemies must be further than this to loot).
    /// </summary>
    public const float LootSafetyDistance = 3f;

    /// <summary>
    /// Maximum distance for interactable objects to be considered.
    /// </summary>
    public const float MaxInteractableDistance = 20f;

    /// <summary>
    /// Range for random wandering when idle.
    /// </summary>
    public const float WanderRange = 10f;

    /// <summary>
    /// Small wander range for default wandering.
    /// </summary>
    public const float DefaultWanderRange = 5f;

    /// <summary>
    /// Maximum number of inactive enemy attempts before removal.
    /// </summary>
    public const int MaxInactiveEnemyAttempts = 10;

    /// <summary>
    /// Standard delay between actions (in seconds).
    /// </summary>
    public const float StandardActionDelay = 0.3333f;

    /// <summary>
    /// Delay for stash operations (in seconds).
    /// </summary>
    public const float StashOperationDelay = 0.1f;

    /// <summary>
    /// Delay after death respawn (in seconds).
    /// </summary>
    public const float DeathRespawnDelay = 1f;

    /// <summary>
    /// Delay for portal operations (in seconds).
    /// </summary>
    public const float PortalDelay = 2f;

    /// <summary>
    /// Delay for reward collection (in seconds).
    /// </summary>
    public const float RewardCollectionDelay = 1f;

    /// <summary>
    /// Delay for idle recovery (in seconds).
    /// </summary>
    public const float IdleRecoveryDelay = 2f;
}
