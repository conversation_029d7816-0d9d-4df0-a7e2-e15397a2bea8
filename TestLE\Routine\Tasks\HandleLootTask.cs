using System.Collections;
using TestLE.Routine.Interfaces;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Finds and picks up loot from the ground.
/// Follows SRP by focusing only on loot collection logic.
/// </summary>
public class HandleLootTask : IGameTask
{
    private readonly ILootService _lootService;

    public HandleLootTask(ILootService lootService)
    {
        _lootService = lootService;
    }

    public bool CanExecute()
    {
        return _lootService.HasLootAvailable();
    }

    public IEnumerator Execute()
    {
        yield return _lootService.HandleLoot();
    }
}
