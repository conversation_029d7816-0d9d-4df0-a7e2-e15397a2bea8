-- Example Loot Script
-- This demonstrates loot detection and item management

SCRIPT_NAME = "Example Loot Script"
SCRIPT_DESCRIPTION = "Monitors and analyzes ground items and loot opportunities"
SCRIPT_VERSION = "1.0.0"
SCRIPT_AUTHOR = "TestLE"
SCRIPT_PRIORITY = 120

-- Configuration
local LOOT_RANGE = 20.0
local VALUABLE_ITEM_KEYWORDS = {"unique", "rare", "legendary", "set", "exalted"}
local CHECK_INTERVAL = 2.0

-- State
local lastLootCheck = 0
local totalItemsSeen = 0
local valuableItemsFound = 0

function CanExecute()
    local player = GetPlayer()
    if not player or not Player.IsAlive() then
        return false
    end
    
    -- Only check periodically
    local currentTime = Unity.Time
    if currentTime - lastLootCheck < CHECK_INTERVAL then
        return false
    end
    
    -- Check if there are any ground items
    local groundItems = GetGroundItems()
    return groundItems and #groundItems > 0
end

function Execute()
    local currentTime = Unity.Time
    lastLootCheck = currentTime
    
    local player = GetPlayer()
    if not player then
        return false
    end
    
    local playerPos = Player.GetPosition()
    if not playerPos then
        return false
    end
    
    local groundItems = GetGroundItems()
    if not groundItems or #groundItems == 0 then
        return true
    end
    
    Log("Scanning " .. #groundItems .. " ground items...")
    
    local nearbyItems = {}
    local valuableItems = {}
    local totalValue = 0
    
    -- Analyze each ground item
    for i = 1, #groundItems do
        local item = groundItems[i]
        if item and item.transform then
            local itemPos = item.transform.position
            local distance = Math.Distance(playerPos, itemPos)
            
            if distance <= LOOT_RANGE then
                table.insert(nearbyItems, {item = item, distance = distance})
                totalItemsSeen = totalItemsSeen + 1
                
                -- Check if item appears valuable (this would need actual item data in real implementation)
                local itemName = tostring(item.name or "Unknown Item")
                local isValuable = false
                
                for j = 1, #VALUABLE_ITEM_KEYWORDS do
                    if string.find(string.lower(itemName), VALUABLE_ITEM_KEYWORDS[j]) then
                        isValuable = true
                        break
                    end
                end
                
                if isValuable then
                    table.insert(valuableItems, {item = item, distance = distance, name = itemName})
                    valuableItemsFound = valuableItemsFound + 1
                end
            end
        end
    end
    
    -- Report findings
    if #nearbyItems > 0 then
        Log("Found " .. #nearbyItems .. " items within " .. LOOT_RANGE .. " units")
        
        -- Find closest item
        local closestItem = nil
        local closestDistance = math.huge
        
        for i = 1, #nearbyItems do
            local itemData = nearbyItems[i]
            if itemData.distance < closestDistance then
                closestDistance = itemData.distance
                closestItem = itemData.item
            end
        end
        
        if closestItem then
            Log("Closest item at distance: " .. string.format("%.1f", closestDistance))
        end
        
        -- Report valuable items
        if #valuableItems > 0 then
            Log("VALUABLE ITEMS DETECTED: " .. #valuableItems)
            for i = 1, #valuableItems do
                local valuable = valuableItems[i]
                Log("  - " .. valuable.name .. " (distance: " .. string.format("%.1f", valuable.distance) .. ")")
            end
        end
        
        -- Categorize items by distance
        local veryClose = 0  -- < 5 units
        local close = 0      -- 5-10 units  
        local medium = 0     -- 10-15 units
        local far = 0        -- 15+ units
        
        for i = 1, #nearbyItems do
            local distance = nearbyItems[i].distance
            if distance < 5.0 then
                veryClose = veryClose + 1
            elseif distance < 10.0 then
                close = close + 1
            elseif distance < 15.0 then
                medium = medium + 1
            else
                far = far + 1
            end
        end
        
        Log("Item distribution - Very Close: " .. veryClose .. ", Close: " .. close .. ", Medium: " .. medium .. ", Far: " .. far)
        
        -- Check if we should prioritize looting
        if veryClose > 0 then
            Log("Items very close to player - consider immediate pickup")
        end
        
        if #valuableItems > 0 and not Player.IsInCombat() then
            Log("Valuable items available and not in combat - good time to loot!")
        end
    end
    
    -- Periodic statistics
    if totalItemsSeen > 0 and totalItemsSeen % 50 == 0 then
        local valuablePercent = (valuableItemsFound / totalItemsSeen) * 100
        Log("Loot Statistics - Total seen: " .. totalItemsSeen .. ", Valuable: " .. valuableItemsFound .. " (" .. string.format("%.1f", valuablePercent) .. "%)")
    end
    
    return true
end

-- Helper function to check if an item name contains valuable keywords
function IsValuableItem(itemName)
    if not itemName then
        return false
    end
    
    local lowerName = string.lower(itemName)
    for i = 1, #VALUABLE_ITEM_KEYWORDS do
        if string.find(lowerName, VALUABLE_ITEM_KEYWORDS[i]) then
            return true
        end
    end
    return false
end

function OnLoad()
    Log("Loot script loaded - monitoring for valuable items")
    Log("Valuable keywords: " .. table.concat(VALUABLE_ITEM_KEYWORDS, ", "))
end

function OnUnload()
    if totalItemsSeen > 0 then
        local valuablePercent = (valuableItemsFound / totalItemsSeen) * 100
        Log("Final loot statistics - Total: " .. totalItemsSeen .. ", Valuable: " .. valuableItemsFound .. " (" .. string.format("%.1f", valuablePercent) .. "%)")
    end
    Log("Loot script unloaded")
end
