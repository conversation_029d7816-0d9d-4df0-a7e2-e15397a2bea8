using System.IO;
using System.Text.Json;
using MelonLoader;
using MelonLoader.Utils;

namespace TestLE.Scripting;

/// <summary>
/// Manages script discovery, validation, configuration, and lifecycle.
/// Provides higher-level script management functionality beyond the core LuaManager.
/// </summary>
public class ScriptManager
{
    private static ScriptManager? _instance;
    public static ScriptManager Instance => _instance ??= new ScriptManager();

    private readonly string _configPath;
    private readonly string _scriptsDirectory;
    private ScriptConfiguration _configuration;

    public ScriptConfiguration Configuration => _configuration;
    public bool IsInitialized { get; private set; }

    private ScriptManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "Scripts");
        _configPath = Path.Combine(_scriptsDirectory, "config.json");
        _configuration = new ScriptConfiguration();
    }

    /// <summary>
    /// Initialize the script manager.
    /// </summary>
    public void Initialize()
    {
        if (IsInitialized)
            return;

        try
        {
            // Ensure directories exist
            Directory.CreateDirectory(_scriptsDirectory);

            // Load configuration
            LoadConfiguration();

            // Initialize Lua manager
            LuaManager.Instance.Initialize();

            // Validate loaded scripts
            ValidateScripts();

            IsInitialized = true;
            MelonLogger.Msg("ScriptManager initialized successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize ScriptManager: {ex.Message}");
        }
    }

    /// <summary>
    /// Shutdown the script manager.
    /// </summary>
    public void Shutdown()
    {
        if (!IsInitialized)
            return;

        try
        {
            SaveConfiguration();
            LuaManager.Instance.Shutdown();
            IsInitialized = false;
            MelonLogger.Msg("ScriptManager shutdown complete");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during ScriptManager shutdown: {ex.Message}");
        }
    }

    /// <summary>
    /// Enable or disable a script.
    /// </summary>
    public bool SetScriptEnabled(string scriptName, bool enabled)
    {
        try
        {
            if (_configuration.ScriptSettings.TryGetValue(scriptName, out var settings))
            {
                settings.Enabled = enabled;
            }
            else
            {
                _configuration.ScriptSettings[scriptName] = new ScriptSettings
                {
                    Enabled = enabled,
                    Priority = 100
                };
            }

            SaveConfiguration();
            MelonLogger.Msg($"Script '{scriptName}' {(enabled ? "enabled" : "disabled")}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to set script '{scriptName}' enabled state: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Set script priority.
    /// </summary>
    public bool SetScriptPriority(string scriptName, int priority)
    {
        try
        {
            if (_configuration.ScriptSettings.TryGetValue(scriptName, out var settings))
            {
                settings.Priority = priority;
            }
            else
            {
                _configuration.ScriptSettings[scriptName] = new ScriptSettings
                {
                    Enabled = true,
                    Priority = priority
                };
            }

            SaveConfiguration();
            MelonLogger.Msg($"Script '{scriptName}' priority set to {priority}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to set script '{scriptName}' priority: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Get script settings.
    /// </summary>
    public ScriptSettings GetScriptSettings(string scriptName)
    {
        if (_configuration.ScriptSettings.TryGetValue(scriptName, out var settings))
        {
            return settings;
        }

        // Return default settings
        return new ScriptSettings { Enabled = true, Priority = 100 };
    }

    /// <summary>
    /// Reload a specific script.
    /// </summary>
    public bool ReloadScript(string scriptName)
    {
        try
        {
            MelonLogger.Msg($"Reloading script: {scriptName}");
            return LuaManager.Instance.LoadScript(scriptName);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to reload script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Reload all scripts.
    /// </summary>
    public void ReloadAllScripts()
    {
        try
        {
            MelonLogger.Msg("Reloading all scripts...");
            
            var scriptFiles = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);
            foreach (var filePath in scriptFiles)
            {
                var scriptName = Path.GetFileNameWithoutExtension(filePath);
                LuaManager.Instance.LoadScript(scriptName);
            }

            ValidateScripts();
            MelonLogger.Msg("All scripts reloaded");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error reloading all scripts: {ex.Message}");
        }
    }

    /// <summary>
    /// Get information about all loaded scripts.
    /// </summary>
    public IEnumerable<ScriptInfo> GetLoadedScripts()
    {
        return LuaManager.Instance.LoadedScripts.Values;
    }

    /// <summary>
    /// Validate all loaded scripts and report issues.
    /// </summary>
    private void ValidateScripts()
    {
        try
        {
            var loadedScripts = LuaManager.Instance.LoadedScripts;
            var validScripts = 0;
            var invalidScripts = 0;

            foreach (var scriptInfo in loadedScripts.Values)
            {
                if (ValidateScript(scriptInfo))
                {
                    validScripts++;
                }
                else
                {
                    invalidScripts++;
                }
            }

            MelonLogger.Msg($"Script validation complete: {validScripts} valid, {invalidScripts} invalid");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during script validation: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate a single script.
    /// </summary>
    private bool ValidateScript(ScriptInfo scriptInfo)
    {
        var isValid = true;

        // Check for required functions
        if (!scriptInfo.HasExecute)
        {
            MelonLogger.Warning($"Script '{scriptInfo.Name}' is missing Execute function");
            isValid = false;
        }

        // Check script metadata
        if (string.IsNullOrEmpty(scriptInfo.DisplayName))
        {
            MelonLogger.Warning($"Script '{scriptInfo.Name}' is missing SCRIPT_NAME metadata");
        }

        if (string.IsNullOrEmpty(scriptInfo.Description))
        {
            MelonLogger.Warning($"Script '{scriptInfo.Name}' is missing SCRIPT_DESCRIPTION metadata");
        }

        return isValid;
    }

    /// <summary>
    /// Load configuration from file.
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<ScriptConfiguration>(json);
                if (config != null)
                {
                    _configuration = config;
                    MelonLogger.Msg("Script configuration loaded");
                    return;
                }
            }

            // Create default configuration
            _configuration = new ScriptConfiguration();
            SaveConfiguration();
            MelonLogger.Msg("Created default script configuration");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error loading script configuration: {ex.Message}");
            _configuration = new ScriptConfiguration();
        }
    }

    /// <summary>
    /// Save configuration to file.
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            var json = JsonSerializer.Serialize(_configuration, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error saving script configuration: {ex.Message}");
        }
    }
}

/// <summary>
/// Configuration for script management.
/// </summary>
public class ScriptConfiguration
{
    public Dictionary<string, ScriptSettings> ScriptSettings { get; set; } = new();
    public bool EnableHotReload { get; set; } = true;
    public bool EnableScriptValidation { get; set; } = true;
    public int DefaultPriority { get; set; } = 100;
}

/// <summary>
/// Settings for individual scripts.
/// </summary>
public class ScriptSettings
{
    public bool Enabled { get; set; } = true;
    public int Priority { get; set; } = 100;
    public Dictionary<string, object> Parameters { get; set; } = new();
}
