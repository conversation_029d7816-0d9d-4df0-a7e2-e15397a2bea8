using System.Collections;
using UnityEngine;

namespace TestLE.Routine.Interfaces;

/// <summary>
/// Service interface for managing stash operations.
/// Follows Interface Segregation Principle by providing focused stash-related operations.
/// </summary>
public interface IStashService
{
    IEnumerator StashAllItems();
}

/// <summary>
/// Service interface for loot-related operations.
/// </summary>
public interface ILootService
{
    IEnumerator HandleLoot();
    bool HasLootAvailable();
}

/// <summary>
/// Service interface for combat-related operations.
/// </summary>
public interface ICombatService
{
    (Enemy? enemy, float distance) FindNearestEnemy(Vector3 position, float maxDistance);
    IEnumerator ExecuteCombatRoutine(Enemy enemy, Transform enemyTransform, float distance);
}

/// <summary>
/// Service interface for navigation and movement operations.
/// </summary>
public interface INavigationService
{
    IEnumerator MoveToPosition(Vector3 position, float stoppingDistance = 1f);
    IEnumerator GoToNextMonolith();
    Vector3 GetRandomWanderPosition(Vector3 currentPosition, float range);
}

/// <summary>
/// Service interface for game state management.
/// </summary>
public interface IGameStateService
{
    bool IsPlayerDead();
    bool IsMonolithComplete();
    bool HasObjectives();
    void ResetGameState();
}

/// <summary>
/// Factory interface for creating game tasks.
/// Follows Dependency Inversion Principle by abstracting task creation.
/// </summary>
public interface ITaskFactory
{
    IEnumerable<IGameTask> CreateTasks();
}
