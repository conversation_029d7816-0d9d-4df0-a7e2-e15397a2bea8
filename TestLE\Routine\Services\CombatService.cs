using System.Collections;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Services;

/// <summary>
/// Single Responsibility: Manages all combat-related operations.
/// Implements ICombatService interface following Dependency Inversion Principle.
/// </summary>
public class CombatService : ICombatService
{
    public (Enemy? enemy, float distance) FindNearestEnemy(Vector3 position, float maxDistance)
    {
        return FindHelpers.FindNearestEnemy(position, maxDistance);
    }

    public IEnumerator ExecuteCombatRoutine(Enemy enemy, Transform enemyTransform, float distance)
    {
        if (enemy.Data.gameObject.active)
        {
            yield return CURRENT_ROUTINE!.Run(enemy, enemyTransform, distance);
        }
        else
        {
            PlayerHelpers.MoveTo(enemyTransform.position);
            yield return new WaitForSeconds(0.3333f);
        }
    }
}
