﻿using Il2Cpp;
using TestLE.Utilities;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_ActorVisuals : Patch
{
    public override void Setup()
    {
        Patches_ActorVisuals.OninitialiseAlignmentPostfix += OnInitialiseAlignmentPostfix;
        Patches_ActorVisuals.OnOnDestroyPostfix += OnOnDestroyPostfix;
    }

    private static void OnInitialiseAlignmentPostfix(ActorVisuals __instance, Alignment newAlignment)
    {
        if (__instance.alignment == null)
            return;

        if (__instance.alignment.isFoe[0])
            _ = new Enemy(__instance, MinimapHelpers.CreateIconForActorDisplayInformation(__instance.gameObject.GetComponent<ActorDisplayInformation>()));
    }

    private static void OnOnDestroyPostfix(ActorVisuals __instance)
    {
        for (var i = 0; i < ENEMIES.Count; i++)
        {
            var enemy = ENEMIES[i];
            if (enemy == null)
            {
                ENEMIES.RemoveAt(i);
                i--;
                continue;
            }

            if (enemy.Data != __instance)
                continue;
            
            enemy.RemoveEnemy();
            i--;
        }
    }
}

// using Il2Cpp;
// using HarmonyLib;
// using TestLE.Utilities;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(ActorVisuals), "initialiseAlignment")]
// public class Patch_ActorVisuals_initialiseAlignment
// {
//     public static void Postfix(ActorVisuals __instance)
//     {
//         if (__instance.alignment == null)
//             return;
//
//         if (__instance.alignment.isFoe[0])
//             _ = new Enemy(__instance, MinimapHelpers.CreateIconForActorDisplayInformation(__instance.gameObject.GetComponent<ActorDisplayInformation>()));
//     }
// }
//
// [HarmonyPatch(typeof(ActorVisuals), "OnDestroy")]
// public class Patch_ActorVisuals_OnDestroy
// {
//     public static void Postfix(ActorVisuals __instance)
//     {
//         for (var i = 0; i < ENEMIES.Count; i++)
//         {
//             var enemy = ENEMIES[i];
//             if (enemy == null)
//             {
//                 ENEMIES.RemoveAt(i);
//                 i--;
//                 continue;
//             }
//
//             if (enemy.Data == __instance)
//             {
//                 enemy.RemoveEnemy();
//                 i--;
//             }
//         }
//     }
// }
