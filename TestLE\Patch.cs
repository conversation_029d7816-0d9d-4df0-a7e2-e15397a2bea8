using System.Collections;
using MelonLoader;
using UnityEngine;
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable UnusedMember.Global

namespace TestLE;

public abstract class Patch
{
    public abstract void Setup();
    
    protected static void WaitForPlayer(Action? onPlayerFound)
    {
        MelonCoroutines.Start(WaitForPlayerCoroutine(onPlayerFound));
    }
    
    protected static IEnumerator WaitForPlayerCoroutine(Action? onPlayerFound)
    {
        while (PLAYER == null)
            yield return new WaitForSeconds(0.2f);
        
        onPlayerFound?.Invoke();
    }
    
    protected static void WaitForSeconds(float seconds, Action? onTimeUp)
    {
        MelonCoroutines.Start(WaitForSecondsCoroutine(seconds, onTimeUp));
    }
    
    protected static IEnumerator WaitForSecondsCoroutine(float seconds, Action? onTimeUp)
    {
        yield return new WaitForSeconds(seconds);
        onTimeUp?.Invoke();
    }
    
    protected static void WaitForFrames(int frames, Action? onFramesUp)
    {
        MelonCoroutines.Start(WaitForFramesCoroutine(frames, onFramesUp));
    }
    
    protected static IEnumerator WaitForFramesCoroutine(int frames, Action? onFramesUp)
    {
        for (var i = 0; i < frames; i++)
            yield return null;
        
        onFramesUp?.Invoke();
    }
}
