﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)ilrepack.lib.msbuild.task\2.0.43\build\ILRepack.Lib.MSBuild.Task.targets" Condition="Exists('$(NuGetPackageRoot)ilrepack.lib.msbuild.task\2.0.43\build\ILRepack.Lib.MSBuild.Task.targets')" />
  </ImportGroup>
</Project>