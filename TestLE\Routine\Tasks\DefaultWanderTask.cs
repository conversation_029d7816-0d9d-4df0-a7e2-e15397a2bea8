using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: A fallback task to ensure the bot is always doing something.
/// Follows SRP by focusing only on default wandering behavior.
/// </summary>
public class DefaultWanderTask : IGameTask
{
    private readonly ICombatService _combatService;

    public DefaultWanderTask(ICombatService combatService)
    {
        _combatService = combatService;
    }

    public bool CanExecute()
    {
        // Find nearest enemy
        var (enemy, _) = _combatService.FindNearestEnemy(PLAYER.transform.position, 100);
        return enemy == null;
    }

    public IEnumerator Execute()
    {
        MelonLogger.Msg("No mobs found!");
        PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5)));
        yield return new WaitForSeconds(1f);
    }
}
