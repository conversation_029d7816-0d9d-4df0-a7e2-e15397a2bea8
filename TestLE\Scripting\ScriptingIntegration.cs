using MelonLoader;
using TestLE.Routine.Factories;
using TestLE.Routine.Interfaces;

namespace TestLE.Scripting;

/// <summary>
/// Integration point for the scripting system with the main mod.
/// Handles initialization, shutdown, and integration with the routine system.
/// </summary>
public static class ScriptingIntegration
{
    private static bool _isInitialized = false;
    private static bool _useScriptableTasks = true;

    /// <summary>
    /// Initialize the scripting system.
    /// Call this from your mod's OnInitializeMetaData or OnApplicationStart.
    /// </summary>
    public static void Initialize()
    {
        if (_isInitialized)
            return;

        try
        {
            MelonLogger.Msg("Initializing TestLE Scripting System...");

            // Initialize the scriptable service provider
            ScriptableServiceProvider.Initialize();

            _isInitialized = true;
            MelonLogger.Msg("TestLE Scripting System initialized successfully!");
            
            // Log some information about loaded scripts
            LogScriptStatus();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize scripting system: {ex.Message}");
            MelonLogger.Error(ex.StackTrace);
        }
    }

    /// <summary>
    /// Shutdown the scripting system.
    /// Call this from your mod's OnApplicationQuit.
    /// </summary>
    public static void Shutdown()
    {
        if (!_isInitialized)
            return;

        try
        {
            MelonLogger.Msg("Shutting down TestLE Scripting System...");
            ScriptableServiceProvider.Shutdown();
            _isInitialized = false;
            MelonLogger.Msg("TestLE Scripting System shutdown complete.");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during scripting system shutdown: {ex.Message}");
        }
    }

    /// <summary>
    /// Get the task factory that includes scriptable tasks.
    /// Use this instead of the regular ServiceProvider.TaskFactory to enable scripts.
    /// </summary>
    public static ITaskFactory GetTaskFactory()
    {
        if (!_isInitialized)
        {
            MelonLogger.Warning("Scripting system not initialized, falling back to regular task factory");
            return ServiceProvider.TaskFactory;
        }

        return _useScriptableTasks ? ScriptableServiceProvider.TaskFactory : ServiceProvider.TaskFactory;
    }

    /// <summary>
    /// Enable or disable scriptable tasks.
    /// </summary>
    public static void SetScriptableTasksEnabled(bool enabled)
    {
        _useScriptableTasks = enabled;
        MelonLogger.Msg($"Scriptable tasks {(enabled ? "enabled" : "disabled")}");
    }

    /// <summary>
    /// Reload all scripts and update the task factory.
    /// </summary>
    public static void ReloadScripts()
    {
        if (!_isInitialized)
        {
            MelonLogger.Warning("Scripting system not initialized");
            return;
        }

        try
        {
            ScriptableServiceProvider.ReloadScripts();
            LogScriptStatus();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error reloading scripts: {ex.Message}");
        }
    }

    /// <summary>
    /// Get information about the current scripting system status.
    /// </summary>
    public static ScriptingStatus GetStatus()
    {
        return new ScriptingStatus
        {
            IsInitialized = _isInitialized,
            ScriptableTasksEnabled = _useScriptableTasks,
            LoadedScriptCount = _isInitialized ? ScriptManager.Instance.LoadedScripts.Count : 0,
            LuaManagerInitialized = _isInitialized && LuaManager.Instance.IsInitialized
        };
    }

    /// <summary>
    /// Log current script status.
    /// </summary>
    private static void LogScriptStatus()
    {
        try
        {
            if (!_isInitialized)
                return;

            var scriptManager = ScriptManager.Instance;
            var loadedScripts = scriptManager.GetLoadedScripts().ToList();
            
            MelonLogger.Msg($"Loaded {loadedScripts.Count} scripts:");
            
            foreach (var script in loadedScripts.OrderBy(s => s.Priority).Reverse())
            {
                var status = script.IsEnabled ? "ENABLED" : "DISABLED";
                var displayName = !string.IsNullOrEmpty(script.DisplayName) ? script.DisplayName : script.Name;
                MelonLogger.Msg($"  - {displayName} (Priority: {script.Priority}, Status: {status})");
                
                if (!script.HasExecute)
                {
                    MelonLogger.Warning($"    WARNING: Script '{script.Name}' missing Execute function");
                }
            }

            if (loadedScripts.Count == 0)
            {
                MelonLogger.Msg("  No scripts loaded. Place .lua files in the Scripts directory to get started.");
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error logging script status: {ex.Message}");
        }
    }

    /// <summary>
    /// Enable a specific script.
    /// </summary>
    public static bool EnableScript(string scriptName)
    {
        if (!_isInitialized)
            return false;

        return ScriptManager.Instance.SetScriptEnabled(scriptName, true);
    }

    /// <summary>
    /// Disable a specific script.
    /// </summary>
    public static bool DisableScript(string scriptName)
    {
        if (!_isInitialized)
            return false;

        return ScriptManager.Instance.SetScriptEnabled(scriptName, false);
    }

    /// <summary>
    /// Set the priority of a specific script.
    /// </summary>
    public static bool SetScriptPriority(string scriptName, int priority)
    {
        if (!_isInitialized)
            return false;

        return ScriptManager.Instance.SetScriptPriority(scriptName, priority);
    }

    /// <summary>
    /// Check if the scripting system is ready to use.
    /// </summary>
    public static bool IsReady => _isInitialized && LuaManager.Instance.IsInitialized;
}

/// <summary>
/// Status information about the scripting system.
/// </summary>
public class ScriptingStatus
{
    public bool IsInitialized { get; set; }
    public bool ScriptableTasksEnabled { get; set; }
    public int LoadedScriptCount { get; set; }
    public bool LuaManagerInitialized { get; set; }

    public override string ToString()
    {
        return $"Scripting System - Initialized: {IsInitialized}, Tasks Enabled: {ScriptableTasksEnabled}, " +
               $"Scripts Loaded: {LoadedScriptCount}, Lua Ready: {LuaManagerInitialized}";
    }
}
