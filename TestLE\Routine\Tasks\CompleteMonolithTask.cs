using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routine.Tasks;

/// <summary>
/// Single Responsibility: Handles the entire sequence of completing a monolith.
/// This task orchestrates multiple services to complete the monolith workflow.
/// </summary>
public class CompleteMonolithTask : IGameTask
{
    private readonly IStashService _stashService;
    private readonly ILootService _lootService;
    private readonly INavigationService _navigationService;
    private readonly IGameStateService _gameStateService;

    public CompleteMonolithTask(
        IStashService stashService, 
        ILootService lootService, 
        INavigationService navigationService,
        IGameStateService gameStateService)
    {
        _stashService = stashService;
        _lootService = lootService;
        _navigationService = navigationService;
        _gameStateService = gameStateService;
    }

    public bool CanExecute() => _gameStateService.IsMonolithComplete();

    public IEnumerator Execute()
    {
        // Stop player by moving to the current position
        PlayerHelpers.MoveTo(PLAYER.transform.position);
        yield return new WaitForSeconds(1f);

        // Make portal
        PlayerHelpers.UsePortal();
        yield return new WaitForSeconds(2f);

        // Find portal
        var portal = FindHelpers.FindMonolithPortal();
        if (portal == null)
        {
            MelonLogger.Msg("Portal not found!");
            yield break;
        }

        // Wait for all ground items to be spawned
        if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
            yield return new WaitForSeconds(1f);

        // Loot all ground items before moving to portal
        yield return LootAllGroundItems();

        // Move to portal
        yield return _navigationService.MoveToPosition(portal.transform.position);

        // Click on portal
        portal.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Clear enemies
        _gameStateService.ResetGameState();

        // Handle reward collection
        yield return CollectRewards();

        // Stash all items
        yield return _stashService.StashAllItems();

        // Move to next monolith
        yield return _navigationService.GoToNextMonolith();
        MelonLogger.Msg("Monolith completed!");
    }

    private IEnumerator LootAllGroundItems()
    {
        while (GROUND_ITEMS.Count > 0)
        {
            yield return _lootService.HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }
    }

    private IEnumerator CollectRewards()
    {
        // Click on reward chest
        var chest = FindHelpers.FindMonolithCompleteRewardChest();
        if (chest.obj != null && chest.isActive)
        {
            yield return _navigationService.MoveToPosition(chest.obj.transform.position);
            chest.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Chest not found!");
        }

        // Loot all ground items
        yield return LootAllGroundItems();

        // Click on reward rock
        var rock = FindHelpers.FindMonolithCompleteRewardRock();
        if (rock.obj != null && rock.isActive)
        {
            yield return _navigationService.MoveToPosition(rock.obj.transform.position);
            rock.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Rock not found!");
        }

        // Wait for loot to finish spawning
        yield return new WaitForSeconds(1f);

        // Move and collect XP tomes
        var tomes = FindHelpers.FindGroundXPTomes();
        foreach (var tome in tomes)
        {
            yield return _navigationService.MoveToPosition(tome.transform.position);
        }

        // Final loot collection
        yield return LootAllGroundItems();
    }
}
