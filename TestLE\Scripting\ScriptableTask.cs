using System.Collections;
using MelonLoader;
using TestLE.Routine.Interfaces;

namespace TestLE.Scripting;

/// <summary>
/// A game task that executes Lua scripts as part of the routine system.
/// Integrates scripted behavior with the existing task-based architecture.
/// </summary>
public class ScriptableTask : IGameTask
{
    private readonly string _scriptName;
    private readonly int _priority;
    private readonly LuaManager _luaManager;

    public string ScriptName => _scriptName;
    public int Priority => _priority;

    public ScriptableTask(string scriptName, int priority = 100)
    {
        _scriptName = scriptName;
        _priority = priority;
        _luaManager = LuaManager.Instance;
    }

    /// <summary>
    /// Determines if this script can be executed in the current game state.
    /// Calls the script's CanExecute function if it exists.
    /// </summary>
    public bool CanExecute()
    {
        try
        {
            if (!_luaManager.IsInitialized)
                return false;

            // Check if the script is loaded and enabled
            if (!_luaManager.LoadedScripts.TryGetValue(_scriptName, out var scriptInfo))
                return false;

            if (!scriptInfo.IsEnabled)
                return false;

            // If the script doesn't have a CanExecute function, assume it can always execute
            if (!scriptInfo.HasCanExecute)
                return true;

            // Call the script's CanExecute function
            return _luaManager.CanExecuteScript(_scriptName);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in ScriptableTask.CanExecute for script '{_scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Execute the script's main Execute function.
    /// Returns an IEnumerator for the game's coroutine scheduler.
    /// </summary>
    public IEnumerator Execute()
    {
        try
        {
            if (!_luaManager.IsInitialized)
            {
                MelonLogger.Warning($"LuaManager not initialized, cannot execute script '{_scriptName}'");
                yield break;
            }

            // Get script info
            if (!_luaManager.LoadedScripts.TryGetValue(_scriptName, out var scriptInfo))
            {
                MelonLogger.Warning($"Script '{_scriptName}' not found or not loaded");
                yield break;
            }

            if (!scriptInfo.HasExecute)
            {
                MelonLogger.Warning($"Script '{_scriptName}' does not have an Execute function");
                yield break;
            }

            MelonLogger.Msg($"Executing script: {_scriptName}");

            // Execute the script
            var scriptCoroutine = _luaManager.ExecuteScript(_scriptName);
            if (scriptCoroutine != null)
            {
                yield return scriptCoroutine;
            }
            else
            {
                // If no coroutine is returned, just yield one frame
                yield return null;
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing script '{_scriptName}': {ex.Message}");
            MelonLogger.Error(ex.StackTrace);
            yield break;
        }
    }

    public override string ToString()
    {
        return $"ScriptableTask({_scriptName}, Priority: {_priority})";
    }

    public override bool Equals(object? obj)
    {
        if (obj is ScriptableTask other)
        {
            return _scriptName == other._scriptName;
        }
        return false;
    }

    public override int GetHashCode()
    {
        return _scriptName.GetHashCode();
    }
}

/// <summary>
/// Factory methods for creating scriptable tasks.
/// </summary>
public static class ScriptableTaskFactory
{
    /// <summary>
    /// Create scriptable tasks for all loaded and enabled scripts.
    /// </summary>
    public static IEnumerable<ScriptableTask> CreateScriptableTasks()
    {
        var luaManager = LuaManager.Instance;

        if (!luaManager.IsInitialized)
            return Enumerable.Empty<ScriptableTask>();

        return luaManager.LoadedScripts.Values
            .Where(script => script.IsEnabled && script.HasExecute)
            .Select(script => new ScriptableTask(script.Name, script.Priority))
            .OrderByDescending(task => task.Priority);
    }
}
