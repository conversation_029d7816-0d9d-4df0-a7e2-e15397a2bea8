using System.Collections;
using Il2Cpp;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace TestLE.Routine.Common;

/// <summary>
/// Centralized location for coroutines used by multiple tasks.
/// Follows DRY principle by avoiding code duplication across tasks.
/// </summary>
public static class SharedRoutines
{
    public static IEnumerator GoNextMonolith()
    {
        var stone = FindHelpers.FindMonolithStone();
        if (stone == null) 
            yield break;

        yield return PlayerHelpers.MoveToForce(stone.transform.position);
        stone.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        var islands = FindHelpers.FindMonolithIslands();
        if (islands.Count == 0) 
            yield break;

        foreach (var (_, ui) in islands)
        {
            if (ui.island.completed) 
                continue;
            
            if (ui.island.islandType is not (EchoWebIsland.IslandType.Normal or EchoWebIsland.IslandType.Arena or EchoWebIsland.IslandType.Beacon)) 
                continue;

            var hasConnectionWithCompleted = ui.island.connectedHexes.ToArray().Any(c => islands.GetValueOrDefault(c)?.island.completed ?? false);
            if (!hasConnectionWithCompleted)
                continue;
            
            ui.rightClicked();
            break;
        }

        while (SceneManager.GetActiveScene().name == "M_Rest")
            yield return new WaitForSeconds(1f);

        yield return new WaitForSeconds(1f);
        yield return CURRENT_ROUTINE!.OnNewArea();
    }
}
