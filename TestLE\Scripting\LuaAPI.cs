using System.Collections;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// API bridge that exposes game functionality to Lua scripts.
/// Provides safe access to game objects, utilities, and functions.
/// </summary>
public static class LuaAPI
{
    /// <summary>
    /// Register all API functions and objects with a Lua script.
    /// </summary>
    public static void RegisterAPI(Script script)
    {
        try
        {
            // Register core game objects
            RegisterGameObjects(script);

            // Register utility functions
            RegisterUtilities(script);

            // Register helper functions
            RegisterHelpers(script);

            // Register Unity functions
            RegisterUnityAPI(script);

            // Register logging functions
            RegisterLogging(script);

            MelonLogger.Msg("LuaAPI registered successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to register LuaAPI: {ex.Message}");
        }
    }

    /// <summary>
    /// Register core game objects and globals.
    /// </summary>
    private static void RegisterGameObjects(Script script)
    {
        // Register the Globals class for access to game state
        script.Globals["Game"] = new GameAPI();

        // Register direct access to common objects
        script.Globals["GetPlayer"] = (Func<object?>)(() => Globals.PLAYER);
        script.Globals["GetEnemies"] = (Func<object>)(() => Globals.ENEMIES);
        script.Globals["GetGroundItems"] = (Func<object>)(() => Globals.GROUND_ITEMS);
        script.Globals["GetInteractables"] = (Func<object>)(() => Globals.INTERACTABLES);
        script.Globals["GetMonolithObjectives"] = (Func<object>)(() => Globals.MONOLITH_OBJECTIVES);
        script.Globals["GetGoodShrines"] = (Func<object>)(() => Globals.GOOD_SHRINES);
    }

    /// <summary>
    /// Register utility functions.
    /// </summary>
    private static void RegisterUtilities(Script script)
    {
        script.Globals["Math"] = new MathAPI();
        script.Globals["Player"] = new PlayerAPI();
        script.Globals["Find"] = new FindAPI();
        script.Globals["Unity"] = new UnityAPI();
    }

    /// <summary>
    /// Register helper functions for common operations.
    /// </summary>
    private static void RegisterHelpers(Script script)
    {
        // Time and delay functions
        script.Globals["Wait"] = (Func<float, IEnumerator>)(seconds => WaitForSeconds(seconds));
        script.Globals["WaitFrame"] = (Func<IEnumerator>)(() => WaitForFrame());

        // Distance and position functions
        script.Globals["Distance"] = (Func<Vector3, Vector3, float>)((a, b) => Vector3.Distance(a, b));
        script.Globals["Distance2D"] = (Func<Vector3, Vector3, float>)((a, b) =>
            Vector2.Distance(new Vector2(a.x, a.z), new Vector2(b.x, b.z)));
    }

    /// <summary>
    /// Register Unity-specific API functions.
    /// </summary>
    private static void RegisterUnityAPI(Script script)
    {
        // Vector3 constructor
        script.Globals["Vector3"] = (Func<float, float, float, Vector3>)((x, y, z) => new Vector3(x, y, z));

        // Input functions
        script.Globals["GetKey"] = (Func<string, bool>)(key => Input.GetKey(key));
        script.Globals["GetKeyDown"] = (Func<string, bool>)(key => Input.GetKeyDown(key));
        script.Globals["GetKeyUp"] = (Func<string, bool>)(key => Input.GetKeyUp(key));
    }

    /// <summary>
    /// Register logging functions.
    /// </summary>
    private static void RegisterLogging(Script script)
    {
        script.Globals["Log"] = (Action<string>)(message => MelonLogger.Msg($"[Script] {message}"));
        script.Globals["LogWarning"] = (Action<string>)(message => MelonLogger.Warning($"[Script] {message}"));
        script.Globals["LogError"] = (Action<string>)(message => MelonLogger.Error($"[Script] {message}"));
    }

    /// <summary>
    /// Wait for a specified number of seconds.
    /// </summary>
    private static IEnumerator WaitForSeconds(float seconds)
    {
        yield return new WaitForSeconds(seconds);
    }

    /// <summary>
    /// Wait for one frame.
    /// </summary>
    private static IEnumerator WaitForFrame()
    {
        yield return null;
    }
}

/// <summary>
/// Game API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class GameAPI
{
    public object? Player => Globals.PLAYER;
    public object Enemies => Globals.ENEMIES;
    public object GroundItems => Globals.GROUND_ITEMS;
    public object Interactables => Globals.INTERACTABLES;
    public object MonolithObjectives => Globals.MONOLITH_OBJECTIVES;
    public object GoodShrines => Globals.GOOD_SHRINES;
    public string? CurrentScene => Globals.CURRENT_SCENE;
    public bool ShowUI => Globals.SHOW_UI;

    public void SetShowUI(bool show) => Globals.SHOW_UI = show;
}

/// <summary>
/// Math API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class MathAPI
{
    public float Distance(Vector3 a, Vector3 b) => Vector3.Distance(a, b);
    public float Distance2D(Vector3 a, Vector3 b) =>
        Vector2.Distance(new Vector2(a.x, a.z), new Vector2(b.x, b.z));
    public float Magnitude(Vector3 v) => v.magnitude;
    public Vector3 Normalize(Vector3 v) => v.normalized;
    public float Dot(Vector3 a, Vector3 b) => Vector3.Dot(a, b);
    public Vector3 Cross(Vector3 a, Vector3 b) => Vector3.Cross(a, b);
    public float Angle(Vector3 from, Vector3 to) => Vector3.Angle(from, to);
    public Vector3 Lerp(Vector3 a, Vector3 b, float t) => Vector3.Lerp(a, b, t);

    public float Clamp(float value, float min, float max) => Mathf.Clamp(value, min, max);
    public float Clamp01(float value) => Mathf.Clamp01(value);
    public float Abs(float value) => Mathf.Abs(value);
    public float Sin(float value) => Mathf.Sin(value);
    public float Cos(float value) => Mathf.Cos(value);
    public float Sqrt(float value) => Mathf.Sqrt(value);
    public float Random() => UnityEngine.Random.value;
    public float RandomRange(float min, float max) => UnityEngine.Random.Range(min, max);
}

/// <summary>
/// Player API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class PlayerAPI
{
    public Vector3? GetPosition()
    {
        try
        {
            return PlayerHelpers.GetPlayerPosition();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player position: {ex.Message}");
            return null;
        }
    }

    public float? GetHealth()
    {
        try
        {
            return PlayerHelpers.GetPlayerHealth();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player health: {ex.Message}");
            return null;
        }
    }

    public float? GetMaxHealth()
    {
        try
        {
            return PlayerHelpers.GetPlayerMaxHealth();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player max health: {ex.Message}");
            return null;
        }
    }

    public float? GetHealthPercent()
    {
        try
        {
            var health = GetHealth();
            var maxHealth = GetMaxHealth();
            if (health.HasValue && maxHealth.HasValue && maxHealth.Value > 0)
                return health.Value / maxHealth.Value;
            return null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player health percent: {ex.Message}");
            return null;
        }
    }

    public bool IsAlive()
    {
        try
        {
            return PlayerHelpers.IsPlayerAlive();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking if player is alive: {ex.Message}");
            return false;
        }
    }

    public bool IsInCombat()
    {
        try
        {
            return PlayerHelpers.IsPlayerInCombat();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking if player is in combat: {ex.Message}");
            return false;
        }
    }
}

/// <summary>
/// Find API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class FindAPI
{
    public object? FindNearestEnemy(Vector3 position, float maxDistance = float.MaxValue)
    {
        try
        {
            return FindHelpers.FindNearestEnemy(position, maxDistance);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error finding nearest enemy: {ex.Message}");
            return null;
        }
    }

    public object? FindNearestGroundItem(Vector3 position, float maxDistance = float.MaxValue)
    {
        try
        {
            return FindHelpers.FindNearestGroundItem(position, maxDistance);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error finding nearest ground item: {ex.Message}");
            return null;
        }
    }

    public object? FindNearestInteractable(Vector3 position, float maxDistance = float.MaxValue)
    {
        try
        {
            return FindHelpers.FindNearestInteractable(position, maxDistance);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error finding nearest interactable: {ex.Message}");
            return null;
        }
    }
}

/// <summary>
/// Unity API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class UnityAPI
{
    public Vector3 CreateVector3(float x, float y, float z) => new Vector3(x, y, z);
    public Vector2 CreateVector2(float x, float y) => new Vector2(x, y);

    public bool GetKey(string key) => Input.GetKey(key);
    public bool GetKeyDown(string key) => Input.GetKeyDown(key);
    public bool GetKeyUp(string key) => Input.GetKeyUp(key);

    public float Time => UnityEngine.Time.time;
    public float DeltaTime => UnityEngine.Time.deltaTime;
    public float FixedTime => UnityEngine.Time.fixedTime;
    public float FixedDeltaTime => UnityEngine.Time.fixedDeltaTime;

    public Vector3 MousePosition => Input.mousePosition;
    public bool LeftMouseButton => Input.GetMouseButton(0);
    public bool RightMouseButton => Input.GetMouseButton(1);
    public bool MiddleMouseButton => Input.GetMouseButton(2);

    public bool LeftMouseButtonDown => Input.GetMouseButtonDown(0);
    public bool RightMouseButtonDown => Input.GetMouseButtonDown(1);
    public bool MiddleMouseButtonDown => Input.GetMouseButtonDown(2);
}
