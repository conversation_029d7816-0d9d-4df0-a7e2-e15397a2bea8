using System.Collections;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// API bridge that exposes game functionality to Lua scripts.
/// </summary>
public static class LuaAPI
{
    /// <summary>
    /// Register all API functions and objects with a Lua script.
    /// </summary>
    public static void RegisterAPI(Script script)
    {
        try
        {
            // Register direct access to game objects
            script.Globals["GetPlayer"] = (Func<object?>)(() => Globals.PLAYER);
            script.Globals["GetEnemies"] = (Func<object>)(() => Globals.ENEMIES);
            script.Globals["GetGroundItems"] = (Func<object>)(() => Globals.GROUND_ITEMS);
            script.Globals["GetInteractables"] = (Func<object>)(() => Globals.INTERACTABLES);
            script.Globals["GetMonolithObjectives"] = (Func<object>)(() => Globals.MONOLITH_OBJECTIVES);
            script.Globals["GetGoodShrines"] = (Func<object>)(() => Globals.GOOD_SHRINES);

            // Register utility APIs
            script.Globals["Player"] = UserData.Create(new PlayerAPI());
            script.Globals["Find"] = UserData.Create(new FindAPI());
            script.Globals["Math"] = UserData.Create(new MathAPI());
            script.Globals["Unity"] = UserData.Create(new UnityAPI());

            // Register helper functions
            script.Globals["Log"] = (Action<string>)(message => MelonLogger.Msg($"[Script] {message}"));
            script.Globals["LogWarning"] = (Action<string>)(message => MelonLogger.Warning($"[Script] {message}"));
            script.Globals["LogError"] = (Action<string>)(message => MelonLogger.Error($"[Script] {message}"));

            script.Globals["Wait"] = (Func<float, IEnumerator>)(seconds => WaitForSeconds(seconds));
            script.Globals["WaitFrame"] = (Func<IEnumerator>)(() => WaitForFrame());
            script.Globals["Distance"] = (Func<Vector3, Vector3, float>)((a, b) => Vector3.Distance(a, b));
            script.Globals["Distance2D"] = (Func<Vector3, Vector3, float>)((a, b) =>
                Vector2.Distance(new Vector2(a.x, a.z), new Vector2(b.x, b.z)));
            script.Globals["Vector3"] = (Func<float, float, float, Vector3>)((x, y, z) => new Vector3(x, y, z));
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to register LuaAPI: {ex.Message}");
        }
    }

    private static IEnumerator WaitForSeconds(float seconds) => new WaitForSeconds(seconds);
    private static IEnumerator WaitForFrame() { yield return null; }
}

/// <summary>
/// Math API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class MathAPI
{
    public float Distance(Vector3 a, Vector3 b) => Vector3.Distance(a, b);
    public float Distance2D(Vector3 a, Vector3 b) =>
        Vector2.Distance(new Vector2(a.x, a.z), new Vector2(b.x, b.z));
    public float Random() => UnityEngine.Random.value;
    public float RandomRange(float min, float max) => UnityEngine.Random.Range(min, max);
    public float Clamp(float value, float min, float max) => Mathf.Clamp(value, min, max);
}

/// <summary>
/// Player API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class PlayerAPI
{
    public Vector3? GetPosition() => PlayerHelpers.GetPlayerPosition();
    public float? GetHealth() => PlayerHelpers.GetPlayerHealth();
    public float? GetMaxHealth() => PlayerHelpers.GetPlayerMaxHealth();
    public bool IsAlive() => PlayerHelpers.IsPlayerAlive();
    public bool IsInCombat() => PlayerHelpers.IsPlayerInCombat();

    public float? GetHealthPercent()
    {
        var health = GetHealth();
        var maxHealth = GetMaxHealth();
        return health.HasValue && maxHealth.HasValue && maxHealth.Value > 0
            ? health.Value / maxHealth.Value : null;
    }
}

/// <summary>
/// Find API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class FindAPI
{
    public object? FindNearestEnemy(Vector3 position, float maxDistance = float.MaxValue) =>
        FindHelpers.FindNearestEnemy(position, maxDistance);
    public object? FindNearestGroundItem(Vector3 position, float maxDistance = float.MaxValue) =>
        FindHelpers.FindNearestGroundItem(position, maxDistance);
    public object? FindNearestInteractable(Vector3 position, float maxDistance = float.MaxValue) =>
        FindHelpers.FindNearestInteractable(position, maxDistance);
}

/// <summary>
/// Unity API wrapper for Lua scripts.
/// </summary>
[MoonSharpUserData]
public class UnityAPI
{
    public bool GetKey(string key) => Input.GetKey(key);
    public bool GetKeyDown(string key) => Input.GetKeyDown(key);
    public bool GetKeyUp(string key) => Input.GetKeyUp(key);
    public float Time => UnityEngine.Time.time;
    public float DeltaTime => UnityEngine.Time.deltaTime;
}
