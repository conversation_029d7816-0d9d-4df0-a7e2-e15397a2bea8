using System.Collections;

namespace TestLE.Routine.Interfaces;

/// <summary>
/// Defines the contract for any executable bot task.
/// This adheres to the Dependency Inversion Principle, as the main routine
/// will depend on this abstraction, not concrete implementations.
/// </summary>
public interface IGameTask
{
    /// <summary>
    /// Determines if this task can be executed in the current game state.
    /// </summary>
    /// <returns>True if the task can execute, otherwise false.</returns>
    bool CanExecute();

    /// <summary>
    /// The logic to be executed by the task.
    /// </summary>
    /// <returns>An IEnumerator for the game's coroutine scheduler.</returns>
    IEnumerator Execute();
}
