﻿using Il2CppLE.Data;
using MelonLoader;
using TestLE.Types;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_AncientBoneLabel : Patch
{
    public override void Setup()
    {
        Patches_AncientBoneLabel.OnSetGroundTooltipTextPostfix += OnSetGroundTooltipTextPostfix;
    }

    private static void OnSetGroundTooltipTextPostfix(AncientBoneLabel __instance)
    {
        MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Ancient bones found!");
        
        WaitForSeconds(1f, () =>
        {
            if (!__instance.gameObject.active || __instance.visuals.amount < 50)
                return;
            
            _ = new GroundItem(__instance);
            MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Ancient bones added!");
        });
    }
}
