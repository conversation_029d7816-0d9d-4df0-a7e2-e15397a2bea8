﻿// using System.Collections;
//
// namespace TestLE.Routine;
//
// #region Legacy MainRoutine
//
// /// <summary>
// /// Legacy MainRoutine class that now delegates to the new orchestrator.
// /// This maintains backward compatibility while using the new SOLID architecture.
// /// </summary>
// public class MainRoutine : IEnumerator
// {
//     private readonly MainRoutineOrchestrator _orchestrator;
//
//     public MainRoutine()
//     {
//         _orchestrator = new MainRoutineOrchestrator();
//     }
//
//     public object? Current => _orchestrator.Current;
//
//     public bool MoveNext() => _orchestrator.MoveNext();
//
//     public void Reset() => _orchestrator.Reset();
// }
//
// #endregion
//
